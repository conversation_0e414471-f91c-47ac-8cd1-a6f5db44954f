# OP-TEE时间保护级别概念澄清

## 重要澄清

您的理解是完全正确的！我需要澄清一个重要的概念混淆。

## 1. 时间保护级别的正确理解

### 1.1 GP规范中定义的保护级别属性

根据OP-TEE代码和GP规范，只有**两个**时间保护级别属性：

```c
// core/tee/tee_svc.c
const struct tee_props tee_propset_tee[] = {
    {
        .name = "gpd.tee.systemTime.protectionLevel",
        .prop_type = USER_TA_PROP_TYPE_U32,
        .get_prop_func = get_prop_tee_sys_time_prot_level  // 动态查询系统时间保护级别
    },
    {
        .name = "gpd.tee.TAPersistentTime.protectionLevel",
        .prop_type = USER_TA_PROP_TYPE_U32,
        .data = &ta_time_prot_lvl,  // 固定为100
        .len = sizeof(ta_time_prot_lvl)
    },
    // 注意：没有 gpd.tee.REETime.protectionLevel
};
```

### 1.2 各个时间API的保护级别归属

**1. TEE_GetSystemTime() - 有保护级别**
- 对应属性：`gpd.tee.systemTime.protectionLevel`
- 保护级别：取决于底层时间源实现
  - 1000级：使用ARM通用计时器等硬件计时器
  - 100级：使用REE时间但有防回滚保护

**2. TEE_GetTAPersistentTime() - 有保护级别**
- 对应属性：`gpd.tee.TAPersistentTime.protectionLevel`
- 保护级别：固定为100级
- 原因：基于系统时间计算偏移量，存储在内存中

**3. TEE_GetREETime() - 没有独立保护级别**
- 没有对应的GP属性
- 直接从REE获取时间，没有额外保护
- 可以理解为"隐含100级或更低"，但GP规范没有为其定义保护级别

## 2. 系统时间保护级别的确定机制

### 2.1 保护级别查询函数

```c
// core/tee/tee_svc.c
static TEE_Result
get_prop_tee_sys_time_prot_level(struct ts_session *sess __unused,
                                 void *buf, size_t *blen)
{
    uint32_t prot;

    if (*blen < sizeof(prot)) {
        *blen = sizeof(prot);
        return TEE_ERROR_SHORT_BUFFER;
    }
    *blen = sizeof(prot);
    prot = tee_time_get_sys_time_protection_level();  // 调用底层实现
    return copy_to_user(buf, &prot, sizeof(prot));
}
```

### 2.2 不同时间源的保护级别实现

**ARM通用计时器实现（1000级）**：
```c
// core/arch/arm/kernel/tee_time_arm_cntpct.c
uint32_t tee_time_get_sys_time_protection_level(void)
{
    return 1000;  // 硬件控制，REE无法修改
}

TEE_Result tee_time_get_sys_time(TEE_Time *time)
{
    uint64_t cntpct = barrier_read_counter_timer();  // 读取硬件计数器
    uint32_t cntfrq = read_cntfrq();
    
    time->seconds = cntpct / cntfrq;
    time->millis = (cntpct % cntfrq) / (cntfrq / TEE_TIME_MILLIS_BASE);
    
    return TEE_SUCCESS;
}
```

**REE时间实现（100级）**：
```c
// core/kernel/tee_time_ree.c
uint32_t tee_time_get_sys_time_protection_level(void)
{
    return 100;   // 基于REE时间，但有防回滚保护
}

TEE_Result tee_time_get_sys_time(TEE_Time *time)
{
    TEE_Result res;

    res = tee_time_get_ree_time(time);  // 通过RPC获取REE时间
    if (res != TEE_SUCCESS)
        return res;

    // 防回滚保护
    mutex_lock(&time_mu);
    if (time->seconds < prev.seconds ||
        (time->seconds == prev.seconds &&
         time->millis < prev.millis))
        *time = prev; /* REE time was rolled back */
    else
        prev = *time;
    mutex_unlock(&time_mu);

    return res;
}
```

## 3. 关键区别说明

### 3.1 TEE_GetSystemTime vs TEE_GetREETime

| 函数 | 保护级别 | 实现方式 | 安全特性 |
|------|----------|----------|----------|
| `TEE_GetSystemTime()` | 有（100或1000） | 可能基于硬件计时器或REE时间+防回滚 | 有保护机制 |
| `TEE_GetREETime()` | 无独立级别 | 直接RPC调用REE | 无额外保护 |

### 3.2 系统时间的两种实现模式

**模式1：硬件计时器模式（1000级）**
```
TEE_GetSystemTime() → tee_time_get_sys_time() → ARM Generic Timer
                                              → 保护级别：1000
```

**模式2：REE时间+防回滚模式（100级）**
```
TEE_GetSystemTime() → tee_time_get_sys_time() → tee_time_get_ree_time() + 防回滚
                                              → 保护级别：100
```

**直接REE时间（无保护级别）**
```
TEE_GetREETime() → tee_time_get_ree_time() → RPC调用REE
                                          → 无独立保护级别
```

## 4. 实际应用中的正确使用

### 4.1 查询系统时间保护级别

```c
// 正确的查询方式
TEE_Result query_system_time_protection(void)
{
    uint32_t sys_protection_level;
    TEE_Result res;
    
    // 查询系统时间保护级别
    res = TEE_GetPropertyAsU32(TEE_PROPSET_TEE_IMPLEMENTATION,
                               "gpd.tee.systemTime.protectionLevel",
                               &sys_protection_level);
    if (res != TEE_SUCCESS)
        return res;
    
    DMSG("System time protection level: %u", sys_protection_level);
    
    if (sys_protection_level >= 1000) {
        DMSG("High security: Hardware-controlled time source");
    } else {
        DMSG("Basic security: REE-based time source with rollback protection");
    }
    
    return TEE_SUCCESS;
}
```

### 4.2 根据保护级别选择时间源

```c
// 根据安全需求选择合适的时间源
TEE_Result get_secure_timestamp(TEE_Time *timestamp)
{
    uint32_t protection_level;
    TEE_Result res;
    
    // 查询系统时间保护级别
    res = TEE_GetPropertyAsU32(TEE_PROPSET_TEE_IMPLEMENTATION,
                               "gpd.tee.systemTime.protectionLevel",
                               &protection_level);
    if (res != TEE_SUCCESS)
        return res;
    
    if (protection_level >= 1000) {
        // 高安全级别：使用系统时间
        TEE_GetSystemTime(timestamp);
        DMSG("Using hardware-protected system time");
    } else {
        // 低安全级别：可能需要额外验证
        TEE_GetSystemTime(timestamp);  // 仍然使用系统时间，但知道其限制
        DMSG("Using REE-based system time with limited protection");
        
        // 可以选择与REE时间进行一致性检查
        TEE_Time ree_time;
        TEE_GetREETime(&ree_time);
        
        uint32_t diff = abs((int32_t)timestamp->seconds - (int32_t)ree_time.seconds);
        if (diff > 60) {
            EMSG("Large time discrepancy detected");
            return TEE_ERROR_TIME_NOT_SET;
        }
    }
    
    return TEE_SUCCESS;
}
```

## 5. 总结

**关键要点**：
1. **系统时间保护级别**指的是`TEE_GetSystemTime()`的保护级别，不是`TEE_GetREETime()`的
2. **REE时间**没有独立的保护级别属性，它就是直接的REE时间
3. **系统时间**可能基于硬件计时器（1000级）或REE时间+防回滚（100级）
4. **TA持久时间**有独立的保护级别（固定100级），基于系统时间计算偏移

感谢您的指正，这个区别对于正确理解和使用OP-TEE时间API非常重要！
