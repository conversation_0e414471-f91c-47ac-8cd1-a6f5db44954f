#ifndef __KERNEL_ROT_H__
#define __KERNEL_ROT_H__

#include <tee_api_defines_extensions.h>
#include <tee_api_defines.h>
#include <tee_api_types.h>

/** @brief Get rot attr by name
 * 
 * @param attr[in]            The name of attr that will be retrieved
 * @param buf[out]           Buffer to retrived the attr data
 * @param buflen[inout]       Input len of buf, output retrieved data len
 * @retval   0 - SUCCESS, other fail
*/
TEE_Result rot_get_attr_by_name(char *attr, uint8_t *buf, uint32_t *buflen);

/** @brief Indicate rot system that the system is boot up finished 
 * 
 * @retval   0 - SUCCESS, other fail
*/
TEE_Result rot_boot_ended(void);
#endif