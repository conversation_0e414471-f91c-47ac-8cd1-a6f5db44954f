// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2019 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>> for STMicroelectronics.
 */

#include "stm32mp151.dtsi"

/ {
	cpus {
		cpu1: cpu@1 {
			compatible = "arm,cortex-a7";
			clock-frequency = <650000000>;
			device_type = "cpu";
			reg = <1>;
		};
	};

	arm-pmu {
		interrupts = <GIC_SPI 200 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 201 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>, <&cpu1>;
	};

	timer {
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>;
	};

	soc {
		m_can1: can@4400e000 {
			compatible = "bosch,m_can";
			reg = <0x4400e000 0x400>, <0x44011000 0x1400>;
			reg-names = "m_can", "message_ram";
			interrupts = <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "int0", "int1";
			clocks = <&rcc CK_HSE>, <&rcc FDCAN_K>;
			clock-names = "hclk", "cclk";
			bosch,mram-cfg = <0x0 0 0 32 0 0 2 2>;
			status = "disabled";
		};

		m_can2: can@4400f000 {
			compatible = "bosch,m_can";
			reg = <0x4400f000 0x400>, <0x44011000 0x2800>;
			reg-names = "m_can", "message_ram";
			interrupts = <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "int0", "int1";
			clocks = <&rcc CK_HSE>, <&rcc FDCAN_K>;
			clock-names = "hclk", "cclk";
			bosch,mram-cfg = <0x1400 0 0 32 0 0 2 2>;
			status = "disabled";
		};
	};
};
