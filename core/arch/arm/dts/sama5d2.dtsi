// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * sama5d2.dtsi - Device Tree Include file for SAMA5D2 family SoC
 *
 *  Copyright (C) 2015 Atmel,
 *                2015 <PERSON><PERSON><PERSON> Desroches <<EMAIL>>
 */

#include <dt-bindings/dma/at91.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/clock/at91.h>
#include <dt-bindings/iio/adc/at91-sama5d2_adc.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	model = "Atmel SAMA5D2 family SoC";
	compatible = "atmel,sama5d2";
	interrupt-parent = <&aic>;

	aliases {
		serial0 = &uart1;
		serial1 = &uart3;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a5";
			reg = <0>;
			next-level-cache = <&L2>;
			clocks = <&pmc PMC_TYPE_CORE PMC_MCK_PRES>;
			clock-names = "cpu";
		};
	};

	pmu {
		compatible = "arm,cortex-a5-pmu";
		interrupts = <2 IRQ_TYPE_LEVEL_HIGH 0>;
	};

	etb@740000 {
		compatible = "arm,coresight-etb10", "arm,primecell";
		reg = <0x740000 0x1000>;

		clocks = <&pmc PMC_TYPE_CORE PMC_MCK>;
		clock-names = "apb_pclk";

		in-ports {
			port {
				etb_in: endpoint {
					remote-endpoint = <&etm_out>;
				};
			};
		};
	};

	etm@73c000 {
		compatible = "arm,coresight-etm3x", "arm,primecell";
		reg = <0x73c000 0x1000>;

		clocks = <&pmc PMC_TYPE_CORE PMC_MCK>;
		clock-names = "apb_pclk";

		out-ports {
			port {
				etm_out: endpoint {
					remote-endpoint = <&etb_in>;
				};
			};
		};
	};

	memory@20000000 {
		device_type = "memory";
		reg = <0x20000000 0x20000000>;
	};

	clocks {
		slow_xtal: slow_xtal {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <0>;
		};

		main_xtal: main_xtal {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <0>;
		};
	};

	ns_sram: sram@200000 {
		compatible = "atmel,sama5d2-sram", "mmio-sram";
		reg = <0x00200000 0x20000>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x00200000 0x20000>;
		status = "disabled";
		secure-status = "okay";
	};

	ahb {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		nfc_sram: sram@100000 {
			compatible = "mmio-sram";
			no-memory-wc;
			reg = <0x00100000 0x2400>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x00100000 0x2400>;

		};

		usb0: gadget@300000 {
			compatible = "atmel,sama5d3-udc";
			reg = <0x00300000 0x100000
			       0xfc02c000 0x400>;
			interrupts = <42 IRQ_TYPE_LEVEL_HIGH 2>;
			clocks = <&pmc PMC_TYPE_PERIPHERAL 42>, <&pmc PMC_TYPE_CORE PMC_UTMI>;
			clock-names = "pclk", "hclk";
			status = "disabled";
		};

		usb1: ohci@400000 {
			compatible = "atmel,at91rm9200-ohci", "usb-ohci";
			reg = <0x00400000 0x100000>;
			interrupts = <41 IRQ_TYPE_LEVEL_HIGH 2>;
			clocks = <&pmc PMC_TYPE_PERIPHERAL 41>, <&pmc PMC_TYPE_PERIPHERAL 41>, <&pmc PMC_TYPE_SYSTEM 6>;
			clock-names = "ohci_clk", "hclk", "uhpck";
			status = "disabled";
		};

		usb2: ehci@500000 {
			compatible = "atmel,at91sam9g45-ehci", "usb-ehci";
			reg = <0x00500000 0x100000>;
			interrupts = <41 IRQ_TYPE_LEVEL_HIGH 2>;
			clocks = <&pmc PMC_TYPE_CORE PMC_UTMI>, <&pmc PMC_TYPE_PERIPHERAL 41>;
			clock-names = "usb_clk", "ehci_clk";
			status = "disabled";
		};

		L2: cache-controller@a00000 {
			compatible = "arm,pl310-cache";
			reg = <0x00a00000 0x1000>;
			interrupts = <63 IRQ_TYPE_LEVEL_HIGH 4>;
			cache-unified;
			cache-level = <2>;
		};

		ebi: ebi@10000000 {
			compatible = "atmel,sama5d3-ebi";
			#address-cells = <2>;
			#size-cells = <1>;
			atmel,smc = <&hsmc>;
			reg = <0x10000000 0x10000000
			       0x60000000 0x30000000>;
			ranges = <0x0 0x0 0x10000000 0x10000000
				  0x1 0x0 0x60000000 0x10000000
				  0x2 0x0 0x70000000 0x10000000
				  0x3 0x0 0x80000000 0x10000000>;
			clocks = <&pmc PMC_TYPE_CORE PMC_MCK2>;
			status = "disabled";

			nand_controller: nand-controller {
				compatible = "atmel,sama5d3-nand-controller";
				atmel,nfc-sram = <&nfc_sram>;
				atmel,nfc-io = <&nfc_io>;
				ecc-engine = <&pmecc>;
				#address-cells = <2>;
				#size-cells = <1>;
				ranges;
				status = "disabled";
			};
		};

		sdmmc0: sdio-host@a0000000 {
			compatible = "atmel,sama5d2-sdhci";
			reg = <0xa0000000 0x300>;
			interrupts = <31 IRQ_TYPE_LEVEL_HIGH 0>;
			clocks = <&pmc PMC_TYPE_PERIPHERAL 31>, <&pmc PMC_TYPE_GCK 31>, <&pmc PMC_TYPE_CORE PMC_MAIN>;
			clock-names = "hclock", "multclk", "baseclk";
			assigned-clocks = <&pmc PMC_TYPE_GCK 31>;
			assigned-clock-parents = <&pmc PMC_TYPE_CORE PMC_UTMI>;
			assigned-clock-rates = <480000000>;
			status = "disabled";
		};

		sdmmc1: sdio-host@b0000000 {
			compatible = "atmel,sama5d2-sdhci";
			reg = <0xb0000000 0x300>;
			interrupts = <32 IRQ_TYPE_LEVEL_HIGH 0>;
			clocks = <&pmc PMC_TYPE_PERIPHERAL 32>, <&pmc PMC_TYPE_GCK 32>, <&pmc PMC_TYPE_CORE PMC_MAIN>;
			clock-names = "hclock", "multclk", "baseclk";
			assigned-clocks = <&pmc PMC_TYPE_GCK 32>;
			assigned-clock-parents = <&pmc PMC_TYPE_CORE PMC_UTMI>;
			assigned-clock-rates = <480000000>;
			status = "disabled";
		};

		nfc_io: nfc-io@c0000000 {
			compatible = "atmel,sama5d3-nfc-io", "syscon";
			reg = <0xc0000000 0x8000000>;
		};

		apb {
			compatible = "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;

			hlcdc: hlcdc@f0000000 {
				compatible = "atmel,sama5d2-hlcdc";
				reg = <0xf0000000 0x2000>;
				interrupts = <45 IRQ_TYPE_LEVEL_HIGH 0>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 45>, <&pmc PMC_TYPE_SYSTEM 3>, <&clk32k>;
				clock-names = "periph_clk","sys_clk", "slow_clk";
				status = "disabled";

				hlcdc-display-controller {
					compatible = "atmel,hlcdc-display-controller";
					#address-cells = <1>;
					#size-cells = <0>;

					port@0 {
						#address-cells = <1>;
						#size-cells = <0>;
						reg = <0>;
					};
				};

				hlcdc_pwm: hlcdc-pwm {
					compatible = "atmel,hlcdc-pwm";
					#pwm-cells = <3>;
				};
			};

			isc: isc@f0008000 {
				compatible = "atmel,sama5d2-isc";
				reg = <0xf0008000 0x4000>;
				interrupts = <46 IRQ_TYPE_LEVEL_HIGH 5>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 46>, <&pmc PMC_TYPE_SYSTEM 18>, <&pmc PMC_TYPE_GCK 46>;
				clock-names = "hclock", "iscck", "gck";
				#clock-cells = <0>;
				clock-output-names = "isc-mck";
				status = "disabled";
			};

			ramc0: ramc@f000c000 {
				compatible = "atmel,sama5d3-ddramc";
				reg = <0xf000c000 0x200>;
				clocks = <&pmc PMC_TYPE_SYSTEM 2>, <&pmc PMC_TYPE_PERIPHERAL 13>;
				clock-names = "ddrck", "mpddr";
			};

			dma0: dma-controller@f0010000 {
				compatible = "atmel,sama5d4-dma";
				reg = <0xf0010000 0x1000>;
				interrupts = <6 IRQ_TYPE_LEVEL_HIGH 0>;
				#dma-cells = <1>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 6>;
				clock-names = "dma_clk";
			};

			/* Place dma1 here despite its address */
			dma1: dma-controller@f0004000 {
				compatible = "atmel,sama5d4-dma";
				reg = <0xf0004000 0x1000>;
				interrupts = <7 IRQ_TYPE_LEVEL_HIGH 0>;
				#dma-cells = <1>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 7>;
				clock-names = "dma_clk";
			};

			pmc: pmc@f0014000 {
				compatible = "atmel,sama5d2-pmc", "syscon";
				reg = <0xf0014000 0x160>;
				interrupts = <74 IRQ_TYPE_LEVEL_HIGH 7>;
				#clock-cells = <2>;
				clocks = <&clk32k>, <&main_xtal>;
				clock-names = "slow_clk", "main_xtal";
			};

			qspi0: spi@f0020000 {
				compatible = "atmel,sama5d2-qspi";
				reg = <0xf0020000 0x100>, <0xd0000000 0x08000000>;
				reg-names = "qspi_base", "qspi_mmap";
				interrupts = <52 IRQ_TYPE_LEVEL_HIGH 7>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 52>;
				#address-cells = <1>;
				#size-cells = <0>;
				status = "disabled";
			};

			qspi1: spi@f0024000 {
				compatible = "atmel,sama5d2-qspi";
				reg = <0xf0024000 0x100>, <0xd8000000 0x08000000>;
				reg-names = "qspi_base", "qspi_mmap";
				interrupts = <53 IRQ_TYPE_LEVEL_HIGH 7>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 53>;
				#address-cells = <1>;
				#size-cells = <0>;
				status = "disabled";
			};

			sha@f0028000 {
				compatible = "atmel,at91sam9g46-sha";
				reg = <0xf0028000 0x100>;
				interrupts = <12 IRQ_TYPE_LEVEL_HIGH 0>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(30))>;
				dma-names = "tx";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 12>;
				clock-names = "sha_clk";
				status = "okay";
			};

			aes@f002c000 {
				compatible = "atmel,at91sam9g46-aes";
				reg = <0xf002c000 0x100>;
				interrupts = <9 IRQ_TYPE_LEVEL_HIGH 0>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(26))>,
				       <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(27))>;
				dma-names = "tx", "rx";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 9>;
				clock-names = "aes_clk";
				status = "okay";
			};

			spi0: spi@f8000000 {
				compatible = "atmel,at91rm9200-spi";
				reg = <0xf8000000 0x100>;
				interrupts = <33 IRQ_TYPE_LEVEL_HIGH 7>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(6))>,
				       <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(7))>;
				dma-names = "tx", "rx";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 33>;
				clock-names = "spi_clk";
				atmel,fifo-size = <16>;
				#address-cells = <1>;
				#size-cells = <0>;
				status = "disabled";
			};

			ssc0: ssc@f8004000 {
				compatible = "atmel,at91sam9g45-ssc";
				reg = <0xf8004000 0x4000>;
				interrupts = <43 IRQ_TYPE_LEVEL_HIGH 4>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					AT91_XDMAC_DT_PERID(21))>,
				       <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					AT91_XDMAC_DT_PERID(22))>;
				dma-names = "tx", "rx";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 43>;
				clock-names = "pclk";
				status = "disabled";
			};

			macb0: ethernet@f8008000 {
				compatible = "atmel,sama5d2-gem";
				reg = <0xf8008000 0x1000>;
				interrupts = <5  IRQ_TYPE_LEVEL_HIGH 3		/* Queue 0 */
					      66 IRQ_TYPE_LEVEL_HIGH 3          /* Queue 1 */
					      67 IRQ_TYPE_LEVEL_HIGH 3>;        /* Queue 2 */
				#address-cells = <1>;
				#size-cells = <0>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 5>, <&pmc PMC_TYPE_PERIPHERAL 5>;
				clock-names = "hclk", "pclk";
				status = "disabled";
			};

			tcb0: timer@f800c000 {
				compatible = "atmel,sama5d2-tcb", "simple-mfd", "syscon";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0xf800c000 0x100>;
				interrupts = <35 IRQ_TYPE_LEVEL_HIGH 0>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 35>, <&pmc PMC_TYPE_GCK 35>, <&clk32k>;
				clock-names = "t0_clk", "gclk", "slow_clk";
			};

			tcb1: timer@f8010000 {
				compatible = "atmel,sama5d2-tcb", "simple-mfd", "syscon";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0xf8010000 0x100>;
				interrupts = <36 IRQ_TYPE_LEVEL_HIGH 0>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 36>, <&pmc PMC_TYPE_GCK 36>, <&clk32k>;
				clock-names = "t0_clk", "gclk", "slow_clk";
				status = "disabled";
				secure-status = "okay";
			};

			hsmc: hsmc@f8014000 {
				compatible = "atmel,sama5d2-smc", "syscon", "simple-mfd";
				reg = <0xf8014000 0x1000>;
				interrupts = <17 IRQ_TYPE_LEVEL_HIGH 6>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 17>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges;

				pmecc: ecc-engine@f8014070 {
					compatible = "atmel,sama5d2-pmecc";
					reg = <0xf8014070 0x490>,
					      <0xf8014500 0x100>;
				};
			};

			pdmic: pdmic@f8018000 {
				compatible = "atmel,sama5d2-pdmic";
				reg = <0xf8018000 0x124>;
				interrupts = <48 IRQ_TYPE_LEVEL_HIGH 7>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1)
					| AT91_XDMAC_DT_PERID(50))>;
				dma-names = "rx";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 48>, <&pmc PMC_TYPE_GCK 48>;
				clock-names = "pclk", "gclk";
				status = "disabled";
			};

			uart0: serial@f801c000 {
				compatible = "atmel,at91sam9260-usart";
				reg = <0xf801c000 0x100>;
				interrupts = <24 IRQ_TYPE_LEVEL_HIGH 7>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(35))>,
				       <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(36))>;
				dma-names = "tx", "rx";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 24>;
				clock-names = "usart";
				status = "disabled";
			};

			uart1: serial@f8020000 {
				compatible = "atmel,at91sam9260-usart";
				reg = <0xf8020000 0x100>;
				interrupts = <25 IRQ_TYPE_LEVEL_HIGH 7>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(37))>,
				       <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(38))>;
				dma-names = "tx", "rx";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 25>;
				clock-names = "usart";
				status = "disabled";
			};

			uart2: serial@f8024000 {
				compatible = "atmel,at91sam9260-usart";
				reg = <0xf8024000 0x100>;
				interrupts = <26 IRQ_TYPE_LEVEL_HIGH 7>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(39))>,
				       <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(40))>;
				dma-names = "tx", "rx";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 26>;
				clock-names = "usart";
				status = "disabled";
			};

			i2c0: i2c@f8028000 {
				compatible = "atmel,sama5d2-i2c";
				reg = <0xf8028000 0x100>;
				interrupts = <29 IRQ_TYPE_LEVEL_HIGH 7>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(0))>,
				       <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(1))>;
				dma-names = "tx", "rx";
				#address-cells = <1>;
				#size-cells = <0>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 29>;
				atmel,fifo-size = <16>;
				status = "disabled";
			};

			pwm0: pwm@f802c000 {
				compatible = "atmel,sama5d2-pwm";
				reg = <0xf802c000 0x4000>;
				interrupts = <38 IRQ_TYPE_LEVEL_HIGH 7>;
				#pwm-cells = <3>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 38>;
				status = "disabled";
			};

			sfr: sfr@f8030000 {
				compatible = "atmel,sama5d2-sfr", "syscon";
				reg = <0xf8030000 0x98>;
			};

			flx0: flexcom@f8034000 {
				compatible = "atmel,sama5d2-flexcom";
				reg = <0xf8034000 0x200>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 19>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0x0 0xf8034000 0x800>;
				status = "disabled";

				uart5: serial@200 {
					compatible = "atmel,at91sam9260-usart";
					reg = <0x200 0x200>;
					interrupts = <19 IRQ_TYPE_LEVEL_HIGH 7>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 19>;
					clock-names = "usart";
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(11))>,
					       <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(12))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <32>;
					status = "disabled";
				};

				spi2: spi@400 {
					compatible = "atmel,at91rm9200-spi";
					reg = <0x400 0x200>;
					interrupts = <19 IRQ_TYPE_LEVEL_HIGH 7>;
					#address-cells = <1>;
					#size-cells = <0>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 19>;
					clock-names = "spi_clk";
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(11))>,
					       <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(12))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <16>;
					status = "disabled";
				};

				i2c2: i2c@600 {
					compatible = "atmel,sama5d2-i2c";
					reg = <0x600 0x200>;
					interrupts = <19 IRQ_TYPE_LEVEL_HIGH 7>;
					#address-cells = <1>;
					#size-cells = <0>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 19>;
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(11))>,
					       <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(12))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <16>;
					status = "disabled";
				};
			};

			flx1: flexcom@f8038000 {
				compatible = "atmel,sama5d2-flexcom";
				reg = <0xf8038000 0x200>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 20>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0x0 0xf8038000 0x800>;
				status = "disabled";

				uart6: serial@200 {
					compatible = "atmel,at91sam9260-usart";
					reg = <0x200 0x200>;
					interrupts = <20 IRQ_TYPE_LEVEL_HIGH 7>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 20>;
					clock-names = "usart";
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(13))>,
					       <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(14))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <32>;
					status = "disabled";
				};

				spi3: spi@400 {
					compatible = "atmel,at91rm9200-spi";
					reg = <0x400 0x200>;
					interrupts = <20 IRQ_TYPE_LEVEL_HIGH 7>;
					#address-cells = <1>;
					#size-cells = <0>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 20>;
					clock-names = "spi_clk";
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(13))>,
					       <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(14))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <16>;
					status = "disabled";
				};

				i2c3: i2c@600 {
					compatible = "atmel,sama5d2-i2c";
					reg = <0x600 0x200>;
					interrupts = <20 IRQ_TYPE_LEVEL_HIGH 7>;
					#address-cells = <1>;
					#size-cells = <0>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 20>;
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(13))>,
					       <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(14))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <16>;
					status = "disabled";
				};
			};

			securam: sram@f8044000 {
				compatible = "atmel,sama5d2-securam", "mmio-sram";
				reg = <0xf8044000 0x1420>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 51>;
				#address-cells = <1>;
				#size-cells = <1>;
				no-memory-wc;
				ranges = <0 0xf8044000 0x1420>;
				status = "disabled";
				secure-status = "okay";
			};

			reset_controller: rstc@f8048000 {
				compatible = "atmel,sama5d3-rstc";
				reg = <0xf8048000 0x10>;
				clocks = <&clk32k>;
			};

			shutdown_controller: shdwc@f8048010 {
				compatible = "atmel,sama5d2-shdwc";
				reg = <0xf8048010 0x10>;
				clocks = <&clk32k>;
				#address-cells = <1>;
				#size-cells = <0>;
				atmel,wakeup-rtc-timer;
			};

			pit: timer@f8048030 {
				compatible = "atmel,at91sam9260-pit";
				reg = <0xf8048030 0x10>;
				interrupts = <3 IRQ_TYPE_LEVEL_HIGH 5>;
				clocks = <&pmc PMC_TYPE_CORE PMC_MCK2>;
			};

			watchdog: watchdog@f8048040 {
				compatible = "atmel,sama5d4-wdt";
				reg = <0xf8048040 0x10>;
				interrupts = <4 IRQ_TYPE_LEVEL_HIGH 7>;
				clocks = <&clk32k>;
				status = "disabled";
				secure-status = "okay";
			};

			clk32k: sckc@f8048050 {
				compatible = "atmel,sama5d4-sckc";
				reg = <0xf8048050 0x4>;

				clocks = <&slow_xtal>;
				#clock-cells = <0>;
			};

			rtc: rtc@f80480b0 {
				compatible = "atmel,sama5d2-rtc";
				reg = <0xf80480b0 0x30>;
				interrupts = <74 IRQ_TYPE_LEVEL_HIGH 7>;
				clocks = <&clk32k>;
				status = "disabled";
				secure-status = "okay";
			};

			i2s0: i2s@f8050000 {
				compatible = "atmel,sama5d2-i2s";
				reg = <0xf8050000 0x100>;
				interrupts = <54 IRQ_TYPE_LEVEL_HIGH 7>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(31))>,
				       <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(32))>;
				dma-names = "tx", "rx";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 54>, <&pmc PMC_TYPE_GCK 54>;
				clock-names = "pclk", "gclk";
				assigned-clocks = <&pmc PMC_TYPE_CORE PMC_I2S0_MUX>;
				assigned-clock-parents = <&pmc PMC_TYPE_GCK 54>;
				status = "disabled";
			};

			can0: can@f8054000 {
				compatible = "bosch,m_can";
				reg = <0xf8054000 0x4000>, <0x210000 0x1c00>;
				reg-names = "m_can", "message_ram";
				interrupts = <56 IRQ_TYPE_LEVEL_HIGH 7>,
					     <64 IRQ_TYPE_LEVEL_HIGH 7>;
				interrupt-names = "int0", "int1";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 56>, <&pmc PMC_TYPE_GCK 56>;
				clock-names = "hclk", "cclk";
				assigned-clocks = <&pmc PMC_TYPE_GCK 56>;
				assigned-clock-parents = <&pmc PMC_TYPE_CORE PMC_UTMI>;
				assigned-clock-rates = <40000000>;
				bosch,mram-cfg = <0x0 0 0 64 0 0 32 32>;
				status = "disabled";
			};

			spi1: spi@fc000000 {
				compatible = "atmel,at91rm9200-spi";
				reg = <0xfc000000 0x100>;
				interrupts = <34 IRQ_TYPE_LEVEL_HIGH 7>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(8))>,
				       <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(9))>;
				dma-names = "tx", "rx";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 34>;
				clock-names = "spi_clk";
				atmel,fifo-size = <16>;
				#address-cells = <1>;
				#size-cells = <0>;
				status = "disabled";
			};

			uart3: serial@fc008000 {
				compatible = "atmel,at91sam9260-usart";
				reg = <0xfc008000 0x100>;
				interrupts = <27 IRQ_TYPE_LEVEL_HIGH 7>;
				dmas = <&dma1
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(41))>,
				       <&dma1
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(42))>;
				dma-names = "tx", "rx";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 27>;
				clock-names = "usart";
				status = "disabled";
			};

			uart4: serial@fc00c000 {
				compatible = "atmel,at91sam9260-usart";
				reg = <0xfc00c000 0x100>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(43))>,
				       <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(44))>;
				dma-names = "tx", "rx";
				interrupts = <28 IRQ_TYPE_LEVEL_HIGH 7>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 28>;
				clock-names = "usart";
				status = "disabled";
			};

			flx2: flexcom@fc010000 {
				compatible = "atmel,sama5d2-flexcom";
				reg = <0xfc010000 0x200>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 21>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0x0 0xfc010000 0x800>;
				status = "disabled";

				uart7: serial@200 {
					compatible = "atmel,at91sam9260-usart";
					reg = <0x200 0x200>;
					interrupts = <21 IRQ_TYPE_LEVEL_HIGH 7>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 21>;
					clock-names = "usart";
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(15))>,
						<&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(16))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <32>;
					status = "disabled";
				};

				spi4: spi@400 {
					compatible = "atmel,at91rm9200-spi";
					reg = <0x400 0x200>;
					interrupts = <21 IRQ_TYPE_LEVEL_HIGH 7>;
					#address-cells = <1>;
					#size-cells = <0>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 21>;
					clock-names = "spi_clk";
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(15))>,
						<&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(16))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <16>;
					status = "disabled";
				};

				i2c4: i2c@600 {
					compatible = "atmel,sama5d2-i2c";
					reg = <0x600 0x200>;
					interrupts = <21 IRQ_TYPE_LEVEL_HIGH 7>;
					#address-cells = <1>;
					#size-cells = <0>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 21>;
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(15))>,
						<&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(16))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <16>;
					status = "disabled";
				};
			};

			flx3: flexcom@fc014000 {
				compatible = "atmel,sama5d2-flexcom";
				reg = <0xfc014000 0x200>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 22>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0x0 0xfc014000 0x800>;
				status = "disabled";

				uart8: serial@200 {
					compatible = "atmel,at91sam9260-usart";
					reg = <0x200 0x200>;
					interrupts = <22 IRQ_TYPE_LEVEL_HIGH 7>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 22>;
					clock-names = "usart";
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(17))>,
					       <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(18))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <32>;
					status = "disabled";
				};

				spi5: spi@400 {
					compatible = "atmel,at91rm9200-spi";
					reg = <0x400 0x200>;
					interrupts = <22 IRQ_TYPE_LEVEL_HIGH 7>;
					#address-cells = <1>;
					#size-cells = <0>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 22>;
					clock-names = "spi_clk";
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(17))>,
					       <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(18))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <16>;
					status = "disabled";
				};

				i2c5: i2c@600 {
					compatible = "atmel,sama5d2-i2c";
					reg = <0x600 0x200>;
					interrupts = <22 IRQ_TYPE_LEVEL_HIGH 7>;
					#address-cells = <1>;
					#size-cells = <0>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 22>;
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(17))>,
					       <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(18))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <16>;
					status = "disabled";
				};

			};

			flx4: flexcom@fc018000 {
				compatible = "atmel,sama5d2-flexcom";
				reg = <0xfc018000 0x200>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 23>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0x0 0xfc018000 0x800>;
				status = "disabled";

				uart9: serial@200 {
					compatible = "atmel,at91sam9260-usart";
					reg = <0x200 0x200>;
					interrupts = <23 IRQ_TYPE_LEVEL_HIGH 7>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 23>;
					clock-names = "usart";
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(19))>,
					       <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(20))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <32>;
					status = "disabled";
				};

				spi6: spi@400 {
					compatible = "atmel,at91rm9200-spi";
					reg = <0x400 0x200>;
					interrupts = <23 IRQ_TYPE_LEVEL_HIGH 7>;
					#address-cells = <1>;
					#size-cells = <0>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 23>;
					clock-names = "spi_clk";
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(19))>,
					       <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(20))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <16>;
					status = "disabled";
				};

				i2c6: i2c@600 {
					compatible = "atmel,sama5d2-i2c";
					reg = <0x600 0x200>;
					interrupts = <23 IRQ_TYPE_LEVEL_HIGH 7>;
					#address-cells = <1>;
					#size-cells = <0>;
					clocks = <&pmc PMC_TYPE_PERIPHERAL 23>;
					dmas = <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(19))>,
					       <&dma0
						(AT91_XDMAC_DT_MEM_IF(0) |
						 AT91_XDMAC_DT_PER_IF(1) |
						 AT91_XDMAC_DT_PERID(20))>;
					dma-names = "tx", "rx";
					atmel,fifo-size = <16>;
					status = "disabled";
				};
			};

			trng@fc01c000 {
				compatible = "atmel,at91sam9g45-trng";
				reg = <0xfc01c000 0x100>;
				interrupts = <47 IRQ_TYPE_LEVEL_HIGH 0>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 47>;
				status = "disabled";
				secure-status = "okay";
			};

			aic: interrupt-controller@fc020000 {
				#interrupt-cells = <3>;
				compatible = "atmel,sama5d2-aic";
				interrupt-controller;
				reg = <0xfc020000 0x200>;
				atmel,external-irqs = <49>;
			};

			saic: interrupt-controller@f803c000 {
				#interrupt-cells = <3>;
				compatible = "atmel,sama5d2-saic";
				interrupt-controller;
				reg = <0xf803c000 0x200>;
				atmel,external-irqs = <49>;
				status = "disabled";
				secure-status = "okay";
			};

			i2c1: i2c@fc028000 {
				compatible = "atmel,sama5d2-i2c";
				reg = <0xfc028000 0x100>;
				interrupts = <30 IRQ_TYPE_LEVEL_HIGH 7>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(2))>,
				       <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(3))>;
				dma-names = "tx", "rx";
				#address-cells = <1>;
				#size-cells = <0>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 30>;
				atmel,fifo-size = <16>;
				status = "disabled";
			};

			adc: adc@fc030000 {
				compatible = "atmel,sama5d2-adc";
				reg = <0xfc030000 0x100>;
				interrupts = <40 IRQ_TYPE_LEVEL_HIGH 7>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 40>;
				clock-names = "adc_clk";
				dmas = <&dma0 (AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) | AT91_XDMAC_DT_PERID(25))>;
				dma-names = "rx";
				atmel,min-sample-rate-hz = <200000>;
				atmel,max-sample-rate-hz = <20000000>;
				atmel,startup-time-ms = <4>;
				atmel,trigger-edge-type = <IRQ_TYPE_EDGE_RISING>;
				#io-channel-cells = <1>;
				status = "disabled";
			};

			resistive_touch: resistive-touch {
				compatible = "resistive-adc-touch";
				io-channels = <&adc AT91_SAMA5D2_ADC_X_CHANNEL>,
					      <&adc AT91_SAMA5D2_ADC_Y_CHANNEL>,
					      <&adc AT91_SAMA5D2_ADC_P_CHANNEL>;
				io-channel-names = "x", "y", "pressure";
				touchscreen-min-pressure = <50000>;
				status = "disabled";
			};

			pioA: pinctrl@fc038000 {
				compatible = "atmel,sama5d2-pinctrl";
				reg = <0xfc038000 0x600>;
				interrupts = <18 IRQ_TYPE_LEVEL_HIGH 7>,
					     <68 IRQ_TYPE_LEVEL_HIGH 7>,
					     <69 IRQ_TYPE_LEVEL_HIGH 7>,
					     <70 IRQ_TYPE_LEVEL_HIGH 7>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-controller;
				#gpio-cells = <2>;
				clocks = <&pmc PMC_TYPE_PERIPHERAL 18>;
			};

			pioBU: secumod@fc040000 {
				compatible = "atmel,sama5d2-secumod", "syscon";
				reg = <0xfc040000 0x100>;

				gpio-controller;
				#gpio-cells = <2>;
				status = "disabled";
				secure-status = "okay";
			};

			tdes@fc044000 {
				compatible = "atmel,at91sam9g46-tdes";
				reg = <0xfc044000 0x100>;
				interrupts = <11 IRQ_TYPE_LEVEL_HIGH 0>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(28))>,
				       <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(29))>;
				dma-names = "tx", "rx";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 11>;
				clock-names = "tdes_clk";
				status = "okay";
			};

			classd: classd@fc048000 {
				compatible = "atmel,sama5d2-classd";
				reg = <0xfc048000 0x100>;
				interrupts = <59 IRQ_TYPE_LEVEL_HIGH 7>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(47))>;
				dma-names = "tx";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 59>, <&pmc PMC_TYPE_GCK 59>;
				clock-names = "pclk", "gclk";
				status = "disabled";
			};

			i2s1: i2s@fc04c000 {
				compatible = "atmel,sama5d2-i2s";
				reg = <0xfc04c000 0x100>;
				interrupts = <55 IRQ_TYPE_LEVEL_HIGH 7>;
				dmas = <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(33))>,
				       <&dma0
					(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
					 AT91_XDMAC_DT_PERID(34))>;
				dma-names = "tx", "rx";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 55>, <&pmc PMC_TYPE_GCK 55>;
				clock-names = "pclk", "gclk";
				assigned-clocks = <&pmc PMC_TYPE_CORE PMC_I2S1_MUX>;
				assigned-parrents = <&pmc PMC_TYPE_GCK 55>;
				status = "disabled";
			};

			can1: can@fc050000 {
				compatible = "bosch,m_can";
				reg = <0xfc050000 0x4000>, <0x210000 0x3800>;
				reg-names = "m_can", "message_ram";
				interrupts = <57 IRQ_TYPE_LEVEL_HIGH 7>,
					     <65 IRQ_TYPE_LEVEL_HIGH 7>;
				interrupt-names = "int0", "int1";
				clocks = <&pmc PMC_TYPE_PERIPHERAL 57>, <&pmc PMC_TYPE_GCK 57>;
				clock-names = "hclk", "cclk";
				assigned-clocks = <&pmc PMC_TYPE_GCK 57>;
				assigned-clock-parents = <&pmc PMC_TYPE_CORE PMC_UTMI>;
				assigned-clock-rates = <40000000>;
				bosch,mram-cfg = <0x1c00 0 0 64 0 0 32 32>;
				status = "disabled";
			};

			sfrbu: sfr@fc05c000 {
				compatible = "atmel,sama5d2-sfrbu", "syscon";
				reg = <0xfc05c000 0x20>;
			};

			chipid@fc069000 {
				compatible = "atmel,sama5d2-chipid";
				reg = <0xfc069000 0x8>;
			};
		};
	};
};
