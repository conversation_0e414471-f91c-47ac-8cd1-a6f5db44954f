// SPDX-License-Identifier: (GPL-2.0 OR BSD-3-Clause)
/*
 * Copyright (C) Linaro Ltd 2019 - All Rights Reserved
 * Author: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 * Copyright (C) 2020 Mare<PERSON>as<PERSON> <<EMAIL>>
 *
 * DHCOR STM32MP1 variant:
 * DHCR-STM32MP157A-C065-R102-V18-SPI-C-01LG
 * DHCOR PCB number: 586-100 or newer
 * Avenger96 PCB number: 588-200 or newer
 */

/dts-v1/;

#include "stm32mp157.dtsi"
#include "stm32mp15xc.dtsi"
#include "stm32mp15xx-dhcor-som.dtsi"
#include "stm32mp15xx-dhcor-avenger96.dtsi"

/ {
	model = "Arrow Electronics STM32MP157A Avenger96 board";
	compatible = "arrow,stm32mp157a-avenger96", "dh,stm32mp157a-dhcor-som",
		     "st,stm32mp157";
};

&m_can1 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&m_can1_pins_b>;
	pinctrl-1 = <&m_can1_sleep_pins_b>;
	status = "disabled";
};

&m_can2 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&m_can2_pins_a>;
	pinctrl-1 = <&m_can2_sleep_pins_a>;
	status = "disabled";
};
