// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2017 - All Rights Reserved
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>> for STMicroelectronics.
 */
#include <dt-bindings/pinctrl/stm32-pinfunc.h>

&pinctrl {
	adc1_in6_pins_a: adc1-in6-0 {
		pins {
			pinmux = <STM32_PINMUX('F', 12, ANALOG)>;
		};
	};

	adc12_ain_pins_a: adc12-ain-0 {
		pins {
			pinmux = <STM32_PINMUX('C', 3, ANALOG)>, /* ADC1 in13 */
				 <STM32_PINMUX('F', 12, ANALOG)>, /* ADC1 in6 */
				 <STM32_PINMUX('F', 13, ANALOG)>, /* ADC2 in2 */
				 <STM32_PINMUX('F', 14, ANALOG)>; /* ADC2 in6 */
		};
	};

	adc12_ain_pins_b: adc12-ain-1 {
		pins {
			pinmux = <STM32_PINMUX('F', 12, ANALOG)>, /* ADC1 in6 */
				 <STM32_PINMUX('F', 13, ANALOG)>; /* ADC2 in2 */
		};
	};

	adc12_usb_cc_pins_a: adc12-usb-cc-pins-0 {
		pins {
			pinmux = <STM32_PINMUX('A', 4, ANALOG)>, /* ADC12 in18 */
				 <STM32_PINMUX('A', 5, ANALOG)>; /* ADC12 in19 */
		};
	};

	cec_pins_a: cec-0 {
		pins {
			pinmux = <STM32_PINMUX('A', 15, AF4)>;
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	cec_sleep_pins_a: cec-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('A', 15, ANALOG)>; /* HDMI_CEC */
		};
	};

	cec_pins_b: cec-1 {
		pins {
			pinmux = <STM32_PINMUX('B', 6, AF5)>;
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	cec_sleep_pins_b: cec-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('B', 6, ANALOG)>; /* HDMI_CEC */
		};
	};

	dac_ch1_pins_a: dac-ch1-0 {
		pins {
			pinmux = <STM32_PINMUX('A', 4, ANALOG)>;
		};
	};

	dac_ch2_pins_a: dac-ch2-0 {
		pins {
			pinmux = <STM32_PINMUX('A', 5, ANALOG)>;
		};
	};

	dcmi_pins_a: dcmi-0 {
		pins {
			pinmux = <STM32_PINMUX('H', 8,  AF13)>,/* DCMI_HSYNC */
				 <STM32_PINMUX('B', 7,  AF13)>,/* DCMI_VSYNC */
				 <STM32_PINMUX('A', 6,  AF13)>,/* DCMI_PIXCLK */
				 <STM32_PINMUX('H', 9,  AF13)>,/* DCMI_D0 */
				 <STM32_PINMUX('H', 10, AF13)>,/* DCMI_D1 */
				 <STM32_PINMUX('H', 11, AF13)>,/* DCMI_D2 */
				 <STM32_PINMUX('H', 12, AF13)>,/* DCMI_D3 */
				 <STM32_PINMUX('H', 14, AF13)>,/* DCMI_D4 */
				 <STM32_PINMUX('I', 4,  AF13)>,/* DCMI_D5 */
				 <STM32_PINMUX('B', 8,  AF13)>,/* DCMI_D6 */
				 <STM32_PINMUX('E', 6,  AF13)>,/* DCMI_D7 */
				 <STM32_PINMUX('I', 1,  AF13)>,/* DCMI_D8 */
				 <STM32_PINMUX('H', 7,  AF13)>,/* DCMI_D9 */
				 <STM32_PINMUX('I', 3,  AF13)>,/* DCMI_D10 */
				 <STM32_PINMUX('H', 15, AF13)>;/* DCMI_D11 */
			bias-disable;
		};
	};

	dcmi_sleep_pins_a: dcmi-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('H', 8,  ANALOG)>,/* DCMI_HSYNC */
				 <STM32_PINMUX('B', 7,  ANALOG)>,/* DCMI_VSYNC */
				 <STM32_PINMUX('A', 6,  ANALOG)>,/* DCMI_PIXCLK */
				 <STM32_PINMUX('H', 9,  ANALOG)>,/* DCMI_D0 */
				 <STM32_PINMUX('H', 10, ANALOG)>,/* DCMI_D1 */
				 <STM32_PINMUX('H', 11, ANALOG)>,/* DCMI_D2 */
				 <STM32_PINMUX('H', 12, ANALOG)>,/* DCMI_D3 */
				 <STM32_PINMUX('H', 14, ANALOG)>,/* DCMI_D4 */
				 <STM32_PINMUX('I', 4,  ANALOG)>,/* DCMI_D5 */
				 <STM32_PINMUX('B', 8,  ANALOG)>,/* DCMI_D6 */
				 <STM32_PINMUX('E', 6,  ANALOG)>,/* DCMI_D7 */
				 <STM32_PINMUX('I', 1,  ANALOG)>,/* DCMI_D8 */
				 <STM32_PINMUX('H', 7,  ANALOG)>,/* DCMI_D9 */
				 <STM32_PINMUX('I', 3,  ANALOG)>,/* DCMI_D10 */
				 <STM32_PINMUX('H', 15, ANALOG)>;/* DCMI_D11 */
		};
	};

	dcmi_pins_b: dcmi-1 {
		pins {
			pinmux = <STM32_PINMUX('A', 4,  AF13)>,/* DCMI_HSYNC */
				 <STM32_PINMUX('B', 7,  AF13)>,/* DCMI_VSYNC */
				 <STM32_PINMUX('A', 6,  AF13)>,/* DCMI_PIXCLK */
				 <STM32_PINMUX('C', 6,  AF13)>,/* DCMI_D0 */
				 <STM32_PINMUX('H', 10, AF13)>,/* DCMI_D1 */
				 <STM32_PINMUX('H', 11, AF13)>,/* DCMI_D2 */
				 <STM32_PINMUX('E', 1,  AF13)>,/* DCMI_D3 */
				 <STM32_PINMUX('E', 11, AF13)>,/* DCMI_D4 */
				 <STM32_PINMUX('D', 3,  AF13)>,/* DCMI_D5 */
				 <STM32_PINMUX('E', 13, AF13)>,/* DCMI_D6 */
				 <STM32_PINMUX('B', 9,  AF13)>;/* DCMI_D7 */
			bias-disable;
		};
	};

	dcmi_sleep_pins_b: dcmi-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('A', 4,  ANALOG)>,/* DCMI_HSYNC */
				 <STM32_PINMUX('B', 7,  ANALOG)>,/* DCMI_VSYNC */
				 <STM32_PINMUX('A', 6,  ANALOG)>,/* DCMI_PIXCLK */
				 <STM32_PINMUX('C', 6,  ANALOG)>,/* DCMI_D0 */
				 <STM32_PINMUX('H', 10, ANALOG)>,/* DCMI_D1 */
				 <STM32_PINMUX('H', 11, ANALOG)>,/* DCMI_D2 */
				 <STM32_PINMUX('E', 1,  ANALOG)>,/* DCMI_D3 */
				 <STM32_PINMUX('E', 11, ANALOG)>,/* DCMI_D4 */
				 <STM32_PINMUX('D', 3,  ANALOG)>,/* DCMI_D5 */
				 <STM32_PINMUX('E', 13, ANALOG)>,/* DCMI_D6 */
				 <STM32_PINMUX('B', 9,  ANALOG)>;/* DCMI_D7 */
		};
	};

	ethernet0_rgmii_pins_a: rgmii-0 {
		pins1 {
			pinmux = <STM32_PINMUX('G', 5, AF11)>, /* ETH_RGMII_CLK125 */
				 <STM32_PINMUX('G', 4, AF11)>, /* ETH_RGMII_GTX_CLK */
				 <STM32_PINMUX('G', 13, AF11)>, /* ETH_RGMII_TXD0 */
				 <STM32_PINMUX('G', 14, AF11)>, /* ETH_RGMII_TXD1 */
				 <STM32_PINMUX('C', 2, AF11)>, /* ETH_RGMII_TXD2 */
				 <STM32_PINMUX('E', 2, AF11)>, /* ETH_RGMII_TXD3 */
				 <STM32_PINMUX('B', 11, AF11)>, /* ETH_RGMII_TX_CTL */
				 <STM32_PINMUX('C', 1, AF11)>; /* ETH_MDC */
			bias-disable;
			drive-push-pull;
			slew-rate = <2>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('A', 2, AF11)>; /* ETH_MDIO */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins3 {
			pinmux = <STM32_PINMUX('C', 4, AF11)>, /* ETH_RGMII_RXD0 */
				 <STM32_PINMUX('C', 5, AF11)>, /* ETH_RGMII_RXD1 */
				 <STM32_PINMUX('B', 0, AF11)>, /* ETH_RGMII_RXD2 */
				 <STM32_PINMUX('B', 1, AF11)>, /* ETH_RGMII_RXD3 */
				 <STM32_PINMUX('A', 1, AF11)>, /* ETH_RGMII_RX_CLK */
				 <STM32_PINMUX('A', 7, AF11)>; /* ETH_RGMII_RX_CTL */
			bias-disable;
		};
	};

	ethernet0_rgmii_sleep_pins_a: rgmii-sleep-0 {
		pins1 {
			pinmux = <STM32_PINMUX('G', 5, ANALOG)>, /* ETH_RGMII_CLK125 */
				 <STM32_PINMUX('G', 4, ANALOG)>, /* ETH_RGMII_GTX_CLK */
				 <STM32_PINMUX('G', 13, ANALOG)>, /* ETH_RGMII_TXD0 */
				 <STM32_PINMUX('G', 14, ANALOG)>, /* ETH_RGMII_TXD1 */
				 <STM32_PINMUX('C', 2, ANALOG)>, /* ETH_RGMII_TXD2 */
				 <STM32_PINMUX('E', 2, ANALOG)>, /* ETH_RGMII_TXD3 */
				 <STM32_PINMUX('B', 11, ANALOG)>, /* ETH_RGMII_TX_CTL */
				 <STM32_PINMUX('A', 2, ANALOG)>, /* ETH_MDIO */
				 <STM32_PINMUX('C', 1, ANALOG)>, /* ETH_MDC */
				 <STM32_PINMUX('C', 4, ANALOG)>, /* ETH_RGMII_RXD0 */
				 <STM32_PINMUX('C', 5, ANALOG)>, /* ETH_RGMII_RXD1 */
				 <STM32_PINMUX('B', 0, ANALOG)>, /* ETH_RGMII_RXD2 */
				 <STM32_PINMUX('B', 1, ANALOG)>, /* ETH_RGMII_RXD3 */
				 <STM32_PINMUX('A', 1, ANALOG)>, /* ETH_RGMII_RX_CLK */
				 <STM32_PINMUX('A', 7, ANALOG)>; /* ETH_RGMII_RX_CTL */
		};
	};

	ethernet0_rgmii_pins_b: rgmii-1 {
		pins1 {
			pinmux = <STM32_PINMUX('G', 5, AF11)>, /* ETH_RGMII_CLK125 */
				 <STM32_PINMUX('G', 4, AF11)>, /* ETH_RGMII_GTX_CLK */
				 <STM32_PINMUX('G', 13, AF11)>, /* ETH_RGMII_TXD0 */
				 <STM32_PINMUX('G', 14, AF11)>, /* ETH_RGMII_TXD1 */
				 <STM32_PINMUX('C', 2, AF11)>, /* ETH_RGMII_TXD2 */
				 <STM32_PINMUX('E', 2, AF11)>, /* ETH_RGMII_TXD3 */
				 <STM32_PINMUX('B', 11, AF11)>, /* ETH_RGMII_TX_CTL */
				 <STM32_PINMUX('C', 1, AF11)>; /* ETH_MDC */
			bias-disable;
			drive-push-pull;
			slew-rate = <2>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('A', 2, AF11)>; /* ETH_MDIO */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins3 {
			pinmux = <STM32_PINMUX('C', 4, AF11)>, /* ETH_RGMII_RXD0 */
				 <STM32_PINMUX('C', 5, AF11)>, /* ETH_RGMII_RXD1 */
				 <STM32_PINMUX('H', 6, AF11)>, /* ETH_RGMII_RXD2 */
				 <STM32_PINMUX('H', 7, AF11)>, /* ETH_RGMII_RXD3 */
				 <STM32_PINMUX('A', 1, AF11)>, /* ETH_RGMII_RX_CLK */
				 <STM32_PINMUX('A', 7, AF11)>; /* ETH_RGMII_RX_CTL */
			bias-disable;
		};
	};

	ethernet0_rgmii_sleep_pins_b: rgmii-sleep-1 {
		pins1 {
			pinmux = <STM32_PINMUX('G', 5, ANALOG)>, /* ETH_RGMII_CLK125 */
				 <STM32_PINMUX('G', 4, ANALOG)>, /* ETH_RGMII_GTX_CLK */
				 <STM32_PINMUX('G', 13, ANALOG)>, /* ETH_RGMII_TXD0 */
				 <STM32_PINMUX('G', 14, ANALOG)>, /* ETH_RGMII_TXD1 */
				 <STM32_PINMUX('C', 2, ANALOG)>, /* ETH_RGMII_TXD2 */
				 <STM32_PINMUX('E', 2, ANALOG)>, /* ETH_RGMII_TXD3 */
				 <STM32_PINMUX('B', 11, ANALOG)>, /* ETH_RGMII_TX_CTL */
				 <STM32_PINMUX('C', 1, ANALOG)>, /* ETH_MDC */
				 <STM32_PINMUX('A', 2, ANALOG)>, /* ETH_MDIO */
				 <STM32_PINMUX('C', 4, ANALOG)>, /* ETH_RGMII_RXD0 */
				 <STM32_PINMUX('C', 5, ANALOG)>, /* ETH_RGMII_RXD1 */
				 <STM32_PINMUX('H', 6, ANALOG)>, /* ETH_RGMII_RXD2 */
				 <STM32_PINMUX('H', 7, ANALOG)>, /* ETH_RGMII_RXD3 */
				 <STM32_PINMUX('A', 1, ANALOG)>, /* ETH_RGMII_RX_CLK */
				 <STM32_PINMUX('A', 7, ANALOG)>; /* ETH_RGMII_RX_CTL */
		 };
	};

	ethernet0_rgmii_pins_c: rgmii-2 {
		pins1 {
			pinmux = <STM32_PINMUX('G', 5, AF11)>, /* ETH_RGMII_CLK125 */
				 <STM32_PINMUX('G', 4, AF11)>, /* ETH_RGMII_GTX_CLK */
				 <STM32_PINMUX('B', 12, AF11)>, /* ETH_RGMII_TXD0 */
				 <STM32_PINMUX('G', 14, AF11)>, /* ETH_RGMII_TXD1 */
				 <STM32_PINMUX('C', 2, AF11)>, /* ETH_RGMII_TXD2 */
				 <STM32_PINMUX('E', 2, AF11)>, /* ETH_RGMII_TXD3 */
				 <STM32_PINMUX('G', 11, AF11)>, /* ETH_RGMII_TX_CTL */
				 <STM32_PINMUX('C', 1, AF11)>; /* ETH_MDC */
			bias-disable;
			drive-push-pull;
			slew-rate = <2>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('A', 2, AF11)>; /* ETH_MDIO */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins3 {
			pinmux = <STM32_PINMUX('C', 4, AF11)>, /* ETH_RGMII_RXD0 */
				 <STM32_PINMUX('C', 5, AF11)>, /* ETH_RGMII_RXD1 */
				 <STM32_PINMUX('H', 6, AF11)>, /* ETH_RGMII_RXD2 */
				 <STM32_PINMUX('B', 1, AF11)>, /* ETH_RGMII_RXD3 */
				 <STM32_PINMUX('A', 1, AF11)>, /* ETH_RGMII_RX_CLK */
				 <STM32_PINMUX('A', 7, AF11)>; /* ETH_RGMII_RX_CTL */
			bias-disable;
		};
	};

	ethernet0_rgmii_sleep_pins_c: rgmii-sleep-2 {
		pins1 {
			pinmux = <STM32_PINMUX('G', 5, ANALOG)>, /* ETH_RGMII_CLK125 */
				 <STM32_PINMUX('G', 4, ANALOG)>, /* ETH_RGMII_GTX_CLK */
				 <STM32_PINMUX('B', 12, ANALOG)>, /* ETH_RGMII_TXD0 */
				 <STM32_PINMUX('G', 14, ANALOG)>, /* ETH_RGMII_TXD1 */
				 <STM32_PINMUX('C', 2, ANALOG)>, /* ETH_RGMII_TXD2 */
				 <STM32_PINMUX('E', 2, ANALOG)>, /* ETH_RGMII_TXD3 */
				 <STM32_PINMUX('G', 11, ANALOG)>, /* ETH_RGMII_TX_CTL */
				 <STM32_PINMUX('A', 2, ANALOG)>, /* ETH_MDIO */
				 <STM32_PINMUX('C', 1, ANALOG)>, /* ETH_MDC */
				 <STM32_PINMUX('C', 4, ANALOG)>, /* ETH_RGMII_RXD0 */
				 <STM32_PINMUX('C', 5, ANALOG)>, /* ETH_RGMII_RXD1 */
				 <STM32_PINMUX('H', 6, ANALOG)>, /* ETH_RGMII_RXD2 */
				 <STM32_PINMUX('B', 1, ANALOG)>, /* ETH_RGMII_RXD3 */
				 <STM32_PINMUX('A', 1, ANALOG)>, /* ETH_RGMII_RX_CLK */
				 <STM32_PINMUX('A', 7, ANALOG)>; /* ETH_RGMII_RX_CTL */
		};
	};

	ethernet0_rmii_pins_a: rmii-0 {
		pins1 {
			pinmux = <STM32_PINMUX('G', 13, AF11)>, /* ETH1_RMII_TXD0 */
				 <STM32_PINMUX('G', 14, AF11)>, /* ETH1_RMII_TXD1 */
				 <STM32_PINMUX('B', 11, AF11)>, /* ETH1_RMII_TX_EN */
				 <STM32_PINMUX('A', 1, AF0)>,   /* ETH1_RMII_REF_CLK */
				 <STM32_PINMUX('A', 2, AF11)>,  /* ETH1_MDIO */
				 <STM32_PINMUX('C', 1, AF11)>;  /* ETH1_MDC */
			bias-disable;
			drive-push-pull;
			slew-rate = <2>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('C', 4, AF11)>,  /* ETH1_RMII_RXD0 */
				 <STM32_PINMUX('C', 5, AF11)>,  /* ETH1_RMII_RXD1 */
				 <STM32_PINMUX('A', 7, AF11)>;  /* ETH1_RMII_CRS_DV */
			bias-disable;
		};
	};

	ethernet0_rmii_sleep_pins_a: rmii-sleep-0 {
		pins1 {
			pinmux = <STM32_PINMUX('G', 13, ANALOG)>, /* ETH1_RMII_TXD0 */
				 <STM32_PINMUX('G', 14, ANALOG)>, /* ETH1_RMII_TXD1 */
				 <STM32_PINMUX('B', 11, ANALOG)>, /* ETH1_RMII_TX_EN */
				 <STM32_PINMUX('A', 2, ANALOG)>,  /* ETH1_MDIO */
				 <STM32_PINMUX('C', 1, ANALOG)>,  /* ETH1_MDC */
				 <STM32_PINMUX('C', 4, ANALOG)>,  /* ETH1_RMII_RXD0 */
				 <STM32_PINMUX('C', 5, ANALOG)>,  /* ETH1_RMII_RXD1 */
				 <STM32_PINMUX('A', 1, ANALOG)>,  /* ETH1_RMII_REF_CLK */
				 <STM32_PINMUX('A', 7, ANALOG)>;  /* ETH1_RMII_CRS_DV */
		};
	};

	ethernet0_rmii_pins_b: rmii-1 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 5, AF0)>, /* ETH1_CLK */
				<STM32_PINMUX('C', 1, AF11)>, /* ETH1_MDC */
				<STM32_PINMUX('G', 13, AF11)>, /* ETH1_TXD0 */
				<STM32_PINMUX('G', 14, AF11)>; /* ETH1_TXD1 */
			bias-disable;
			drive-push-pull;
			slew-rate = <1>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('A', 2, AF11)>; /* ETH1_MDIO */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins3 {
			pinmux = <STM32_PINMUX('A', 7, AF11)>, /* ETH1_CRS_DV */
				<STM32_PINMUX('C', 4, AF11)>, /* ETH1_RXD0 */
				<STM32_PINMUX('C', 5, AF11)>; /* ETH1_RXD1 */
			bias-disable;
		};
		pins4 {
			pinmux = <STM32_PINMUX('B', 11, AF11)>; /* ETH1_TX_EN */
		};
	};

	ethernet0_rmii_sleep_pins_b: rmii-sleep-1 {
		pins1 {
			pinmux = <STM32_PINMUX('A', 2, ANALOG)>, /* ETH1_MDIO */
				<STM32_PINMUX('A', 7, ANALOG)>, /* ETH1_CRS_DV */
				<STM32_PINMUX('B', 5, ANALOG)>, /* ETH1_CLK */
				<STM32_PINMUX('B', 11, ANALOG)>, /* ETH1_TX_EN */
				<STM32_PINMUX('C', 1, ANALOG)>, /* ETH1_MDC */
				<STM32_PINMUX('C', 4, ANALOG)>, /* ETH1_RXD0 */
				<STM32_PINMUX('C', 5, ANALOG)>, /* ETH1_RXD1 */
				<STM32_PINMUX('G', 13, ANALOG)>, /* ETH1_TXD0 */
				<STM32_PINMUX('G', 14, ANALOG)>; /* ETH1_TXD1 */
		};
	};

	ethernet0_rmii_pins_c: rmii-2 {
		pins1 {
			pinmux = <STM32_PINMUX('G', 13, AF11)>, /* ETH1_RMII_TXD0 */
				 <STM32_PINMUX('G', 14, AF11)>, /* ETH1_RMII_TXD1 */
				 <STM32_PINMUX('B', 11, AF11)>, /* ETH1_RMII_TX_EN */
				 <STM32_PINMUX('A', 1, AF11)>,  /* ETH1_RMII_REF_CLK */
				 <STM32_PINMUX('A', 2, AF11)>,  /* ETH1_MDIO */
				 <STM32_PINMUX('C', 1, AF11)>;  /* ETH1_MDC */
			bias-disable;
			drive-push-pull;
			slew-rate = <2>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('C', 4, AF11)>,  /* ETH1_RMII_RXD0 */
				 <STM32_PINMUX('C', 5, AF11)>,  /* ETH1_RMII_RXD1 */
				 <STM32_PINMUX('A', 7, AF11)>;  /* ETH1_RMII_CRS_DV */
			bias-disable;
		};
	};

	ethernet0_rmii_sleep_pins_c: rmii-sleep-2 {
		pins1 {
			pinmux = <STM32_PINMUX('G', 13, ANALOG)>, /* ETH1_RMII_TXD0 */
				 <STM32_PINMUX('G', 14, ANALOG)>, /* ETH1_RMII_TXD1 */
				 <STM32_PINMUX('B', 11, ANALOG)>, /* ETH1_RMII_TX_EN */
				 <STM32_PINMUX('A', 2, ANALOG)>,  /* ETH1_MDIO */
				 <STM32_PINMUX('C', 1, ANALOG)>,  /* ETH1_MDC */
				 <STM32_PINMUX('C', 4, ANALOG)>,  /* ETH1_RMII_RXD0 */
				 <STM32_PINMUX('C', 5, ANALOG)>,  /* ETH1_RMII_RXD1 */
				 <STM32_PINMUX('A', 1, ANALOG)>,  /* ETH1_RMII_REF_CLK */
				 <STM32_PINMUX('A', 7, ANALOG)>;  /* ETH1_RMII_CRS_DV */
		};
	};

	fmc_pins_a: fmc-0 {
		pins1 {
			pinmux = <STM32_PINMUX('D', 4, AF12)>, /* FMC_NOE */
				 <STM32_PINMUX('D', 5, AF12)>, /* FMC_NWE */
				 <STM32_PINMUX('D', 11, AF12)>, /* FMC_A16_FMC_CLE */
				 <STM32_PINMUX('D', 12, AF12)>, /* FMC_A17_FMC_ALE */
				 <STM32_PINMUX('D', 14, AF12)>, /* FMC_D0 */
				 <STM32_PINMUX('D', 15, AF12)>, /* FMC_D1 */
				 <STM32_PINMUX('D', 0, AF12)>, /* FMC_D2 */
				 <STM32_PINMUX('D', 1, AF12)>, /* FMC_D3 */
				 <STM32_PINMUX('E', 7, AF12)>, /* FMC_D4 */
				 <STM32_PINMUX('E', 8, AF12)>, /* FMC_D5 */
				 <STM32_PINMUX('E', 9, AF12)>, /* FMC_D6 */
				 <STM32_PINMUX('E', 10, AF12)>, /* FMC_D7 */
				 <STM32_PINMUX('G', 9, AF12)>; /* FMC_NE2_FMC_NCE */
			bias-disable;
			drive-push-pull;
			slew-rate = <1>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 6, AF12)>; /* FMC_NWAIT */
			bias-pull-up;
		};
	};

	fmc_sleep_pins_a: fmc-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('D', 4, ANALOG)>, /* FMC_NOE */
				 <STM32_PINMUX('D', 5, ANALOG)>, /* FMC_NWE */
				 <STM32_PINMUX('D', 11, ANALOG)>, /* FMC_A16_FMC_CLE */
				 <STM32_PINMUX('D', 12, ANALOG)>, /* FMC_A17_FMC_ALE */
				 <STM32_PINMUX('D', 14, ANALOG)>, /* FMC_D0 */
				 <STM32_PINMUX('D', 15, ANALOG)>, /* FMC_D1 */
				 <STM32_PINMUX('D', 0, ANALOG)>, /* FMC_D2 */
				 <STM32_PINMUX('D', 1, ANALOG)>, /* FMC_D3 */
				 <STM32_PINMUX('E', 7, ANALOG)>, /* FMC_D4 */
				 <STM32_PINMUX('E', 8, ANALOG)>, /* FMC_D5 */
				 <STM32_PINMUX('E', 9, ANALOG)>, /* FMC_D6 */
				 <STM32_PINMUX('E', 10, ANALOG)>, /* FMC_D7 */
				 <STM32_PINMUX('D', 6, ANALOG)>, /* FMC_NWAIT */
				 <STM32_PINMUX('G', 9, ANALOG)>; /* FMC_NE2_FMC_NCE */
		};
	};

	fmc_pins_b: fmc-1 {
		pins {
			pinmux = <STM32_PINMUX('D', 4, AF12)>, /* FMC_NOE */
				 <STM32_PINMUX('D', 5, AF12)>, /* FMC_NWE */
				 <STM32_PINMUX('B', 7, AF12)>, /* FMC_NL */
				 <STM32_PINMUX('D', 14, AF12)>, /* FMC_D0 */
				 <STM32_PINMUX('D', 15, AF12)>, /* FMC_D1 */
				 <STM32_PINMUX('D', 0, AF12)>, /* FMC_D2 */
				 <STM32_PINMUX('D', 1, AF12)>, /* FMC_D3 */
				 <STM32_PINMUX('E', 7, AF12)>, /* FMC_D4 */
				 <STM32_PINMUX('E', 8, AF12)>, /* FMC_D5 */
				 <STM32_PINMUX('E', 9, AF12)>, /* FMC_D6 */
				 <STM32_PINMUX('E', 10, AF12)>, /* FMC_D7 */
				 <STM32_PINMUX('E', 11, AF12)>, /* FMC_D8 */
				 <STM32_PINMUX('E', 12, AF12)>, /* FMC_D9 */
				 <STM32_PINMUX('E', 13, AF12)>, /* FMC_D10 */
				 <STM32_PINMUX('E', 14, AF12)>, /* FMC_D11 */
				 <STM32_PINMUX('E', 15, AF12)>, /* FMC_D12 */
				 <STM32_PINMUX('D', 8, AF12)>, /* FMC_D13 */
				 <STM32_PINMUX('D', 9, AF12)>, /* FMC_D14 */
				 <STM32_PINMUX('D', 10, AF12)>, /* FMC_D15 */
				 <STM32_PINMUX('G', 9, AF12)>, /* FMC_NE2_FMC_NCE */
				 <STM32_PINMUX('G', 12, AF12)>; /* FMC_NE4 */
			bias-disable;
			drive-push-pull;
			slew-rate = <3>;
		};
	};

	fmc_sleep_pins_b: fmc-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('D', 4, ANALOG)>, /* FMC_NOE */
				 <STM32_PINMUX('D', 5, ANALOG)>, /* FMC_NWE */
				 <STM32_PINMUX('B', 7, ANALOG)>, /* FMC_NL */
				 <STM32_PINMUX('D', 14, ANALOG)>, /* FMC_D0 */
				 <STM32_PINMUX('D', 15, ANALOG)>, /* FMC_D1 */
				 <STM32_PINMUX('D', 0, ANALOG)>, /* FMC_D2 */
				 <STM32_PINMUX('D', 1, ANALOG)>, /* FMC_D3 */
				 <STM32_PINMUX('E', 7, ANALOG)>, /* FMC_D4 */
				 <STM32_PINMUX('E', 8, ANALOG)>, /* FMC_D5 */
				 <STM32_PINMUX('E', 9, ANALOG)>, /* FMC_D6 */
				 <STM32_PINMUX('E', 10, ANALOG)>, /* FMC_D7 */
				 <STM32_PINMUX('E', 11, ANALOG)>, /* FMC_D8 */
				 <STM32_PINMUX('E', 12, ANALOG)>, /* FMC_D9 */
				 <STM32_PINMUX('E', 13, ANALOG)>, /* FMC_D10 */
				 <STM32_PINMUX('E', 14, ANALOG)>, /* FMC_D11 */
				 <STM32_PINMUX('E', 15, ANALOG)>, /* FMC_D12 */
				 <STM32_PINMUX('D', 8, ANALOG)>, /* FMC_D13 */
				 <STM32_PINMUX('D', 9, ANALOG)>, /* FMC_D14 */
				 <STM32_PINMUX('D', 10, ANALOG)>, /* FMC_D15 */
				 <STM32_PINMUX('G', 9, ANALOG)>, /* FMC_NE2_FMC_NCE */
				 <STM32_PINMUX('G', 12, ANALOG)>; /* FMC_NE4 */
		};
	};

	i2c1_pins_a: i2c1-0 {
		pins {
			pinmux = <STM32_PINMUX('D', 12, AF5)>, /* I2C1_SCL */
				 <STM32_PINMUX('F', 15, AF5)>; /* I2C1_SDA */
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	i2c1_sleep_pins_a: i2c1-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('D', 12, ANALOG)>, /* I2C1_SCL */
				 <STM32_PINMUX('F', 15, ANALOG)>; /* I2C1_SDA */
		};
	};

	i2c1_pins_b: i2c1-1 {
		pins {
			pinmux = <STM32_PINMUX('F', 14, AF5)>, /* I2C1_SCL */
				 <STM32_PINMUX('F', 15, AF5)>; /* I2C1_SDA */
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	i2c1_sleep_pins_b: i2c1-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('F', 14, ANALOG)>, /* I2C1_SCL */
				 <STM32_PINMUX('F', 15, ANALOG)>; /* I2C1_SDA */
		};
	};

	i2c2_pins_a: i2c2-0 {
		pins {
			pinmux = <STM32_PINMUX('H', 4, AF4)>, /* I2C2_SCL */
				 <STM32_PINMUX('H', 5, AF4)>; /* I2C2_SDA */
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	i2c2_sleep_pins_a: i2c2-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('H', 4, ANALOG)>, /* I2C2_SCL */
				 <STM32_PINMUX('H', 5, ANALOG)>; /* I2C2_SDA */
		};
	};

	i2c2_pins_b1: i2c2-1 {
		pins {
			pinmux = <STM32_PINMUX('H', 5, AF4)>; /* I2C2_SDA */
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	i2c2_sleep_pins_b1: i2c2-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('H', 5, ANALOG)>; /* I2C2_SDA */
		};
	};

	i2c2_pins_c: i2c2-2 {
		pins {
			pinmux = <STM32_PINMUX('F', 1, AF4)>, /* I2C2_SCL */
				 <STM32_PINMUX('H', 5, AF4)>; /* I2C2_SDA */
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	i2c2_pins_sleep_c: i2c2-sleep-2 {
		pins {
			pinmux = <STM32_PINMUX('F', 1, ANALOG)>, /* I2C2_SCL */
				 <STM32_PINMUX('H', 5, ANALOG)>; /* I2C2_SDA */
		};
	};

	i2c5_pins_a: i2c5-0 {
		pins {
			pinmux = <STM32_PINMUX('A', 11, AF4)>, /* I2C5_SCL */
				 <STM32_PINMUX('A', 12, AF4)>; /* I2C5_SDA */
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	i2c5_sleep_pins_a: i2c5-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('A', 11, ANALOG)>, /* I2C5_SCL */
				 <STM32_PINMUX('A', 12, ANALOG)>; /* I2C5_SDA */

		};
	};

	i2c5_pins_b: i2c5-1 {
		pins {
			pinmux = <STM32_PINMUX('D', 0, AF4)>, /* I2C5_SCL */
				 <STM32_PINMUX('D', 1, AF4)>; /* I2C5_SDA */
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	i2c5_sleep_pins_b: i2c5-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('D', 0, ANALOG)>, /* I2C5_SCL */
				 <STM32_PINMUX('D', 1, ANALOG)>; /* I2C5_SDA */
		};
	};

	i2s2_pins_a: i2s2-0 {
		pins {
			pinmux = <STM32_PINMUX('I', 3, AF5)>, /* I2S2_SDO */
				 <STM32_PINMUX('B', 9, AF5)>, /* I2S2_WS */
				 <STM32_PINMUX('A', 9, AF5)>; /* I2S2_CK */
			slew-rate = <1>;
			drive-push-pull;
			bias-disable;
		};
	};

	i2s2_sleep_pins_a: i2s2-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('I', 3, ANALOG)>, /* I2S2_SDO */
				 <STM32_PINMUX('B', 9, ANALOG)>, /* I2S2_WS */
				 <STM32_PINMUX('A', 9, ANALOG)>; /* I2S2_CK */
		};
	};

	ltdc_pins_a: ltdc-0 {
		pins {
			pinmux = <STM32_PINMUX('G',  7, AF14)>, /* LCD_CLK */
				 <STM32_PINMUX('I', 10, AF14)>, /* LCD_HSYNC */
				 <STM32_PINMUX('I',  9, AF14)>, /* LCD_VSYNC */
				 <STM32_PINMUX('F', 10, AF14)>, /* LCD_DE */
				 <STM32_PINMUX('H',  2, AF14)>, /* LCD_R0 */
				 <STM32_PINMUX('H',  3, AF14)>, /* LCD_R1 */
				 <STM32_PINMUX('H',  8, AF14)>, /* LCD_R2 */
				 <STM32_PINMUX('H',  9, AF14)>, /* LCD_R3 */
				 <STM32_PINMUX('H', 10, AF14)>, /* LCD_R4 */
				 <STM32_PINMUX('C',  0, AF14)>, /* LCD_R5 */
				 <STM32_PINMUX('H', 12, AF14)>, /* LCD_R6 */
				 <STM32_PINMUX('E', 15, AF14)>, /* LCD_R7 */
				 <STM32_PINMUX('E',  5, AF14)>, /* LCD_G0 */
				 <STM32_PINMUX('E',  6, AF14)>, /* LCD_G1 */
				 <STM32_PINMUX('H', 13, AF14)>, /* LCD_G2 */
				 <STM32_PINMUX('H', 14, AF14)>, /* LCD_G3 */
				 <STM32_PINMUX('H', 15, AF14)>, /* LCD_G4 */
				 <STM32_PINMUX('I',  0, AF14)>, /* LCD_G5 */
				 <STM32_PINMUX('I',  1, AF14)>, /* LCD_G6 */
				 <STM32_PINMUX('I',  2, AF14)>, /* LCD_G7 */
				 <STM32_PINMUX('D',  9, AF14)>, /* LCD_B0 */
				 <STM32_PINMUX('G', 12, AF14)>, /* LCD_B1 */
				 <STM32_PINMUX('G', 10, AF14)>, /* LCD_B2 */
				 <STM32_PINMUX('D', 10, AF14)>, /* LCD_B3 */
				 <STM32_PINMUX('I',  4, AF14)>, /* LCD_B4 */
				 <STM32_PINMUX('A',  3, AF14)>, /* LCD_B5 */
				 <STM32_PINMUX('B',  8, AF14)>, /* LCD_B6 */
				 <STM32_PINMUX('D',  8, AF14)>; /* LCD_B7 */
			bias-disable;
			drive-push-pull;
			slew-rate = <1>;
		};
	};

	ltdc_sleep_pins_a: ltdc-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('G',  7, ANALOG)>, /* LCD_CLK */
				 <STM32_PINMUX('I', 10, ANALOG)>, /* LCD_HSYNC */
				 <STM32_PINMUX('I',  9, ANALOG)>, /* LCD_VSYNC */
				 <STM32_PINMUX('F', 10, ANALOG)>, /* LCD_DE */
				 <STM32_PINMUX('H',  2, ANALOG)>, /* LCD_R0 */
				 <STM32_PINMUX('H',  3, ANALOG)>, /* LCD_R1 */
				 <STM32_PINMUX('H',  8, ANALOG)>, /* LCD_R2 */
				 <STM32_PINMUX('H',  9, ANALOG)>, /* LCD_R3 */
				 <STM32_PINMUX('H', 10, ANALOG)>, /* LCD_R4 */
				 <STM32_PINMUX('C',  0, ANALOG)>, /* LCD_R5 */
				 <STM32_PINMUX('H', 12, ANALOG)>, /* LCD_R6 */
				 <STM32_PINMUX('E', 15, ANALOG)>, /* LCD_R7 */
				 <STM32_PINMUX('E',  5, ANALOG)>, /* LCD_G0 */
				 <STM32_PINMUX('E',  6, ANALOG)>, /* LCD_G1 */
				 <STM32_PINMUX('H', 13, ANALOG)>, /* LCD_G2 */
				 <STM32_PINMUX('H', 14, ANALOG)>, /* LCD_G3 */
				 <STM32_PINMUX('H', 15, ANALOG)>, /* LCD_G4 */
				 <STM32_PINMUX('I',  0, ANALOG)>, /* LCD_G5 */
				 <STM32_PINMUX('I',  1, ANALOG)>, /* LCD_G6 */
				 <STM32_PINMUX('I',  2, ANALOG)>, /* LCD_G7 */
				 <STM32_PINMUX('D',  9, ANALOG)>, /* LCD_B0 */
				 <STM32_PINMUX('G', 12, ANALOG)>, /* LCD_B1 */
				 <STM32_PINMUX('G', 10, ANALOG)>, /* LCD_B2 */
				 <STM32_PINMUX('D', 10, ANALOG)>, /* LCD_B3 */
				 <STM32_PINMUX('I',  4, ANALOG)>, /* LCD_B4 */
				 <STM32_PINMUX('A',  3, ANALOG)>, /* LCD_B5 */
				 <STM32_PINMUX('B',  8, ANALOG)>, /* LCD_B6 */
				 <STM32_PINMUX('D',  8, ANALOG)>; /* LCD_B7 */
		};
	};

	ltdc_pins_b: ltdc-1 {
		pins {
			pinmux = <STM32_PINMUX('I', 14, AF14)>, /* LCD_CLK */
				 <STM32_PINMUX('I', 12, AF14)>, /* LCD_HSYNC */
				 <STM32_PINMUX('I', 13, AF14)>, /* LCD_VSYNC */
				 <STM32_PINMUX('K',  7, AF14)>, /* LCD_DE */
				 <STM32_PINMUX('I', 15, AF14)>, /* LCD_R0 */
				 <STM32_PINMUX('J',  0, AF14)>, /* LCD_R1 */
				 <STM32_PINMUX('J',  1, AF14)>, /* LCD_R2 */
				 <STM32_PINMUX('J',  2, AF14)>, /* LCD_R3 */
				 <STM32_PINMUX('J',  3, AF14)>, /* LCD_R4 */
				 <STM32_PINMUX('J',  4, AF14)>, /* LCD_R5 */
				 <STM32_PINMUX('J',  5, AF14)>, /* LCD_R6 */
				 <STM32_PINMUX('J',  6, AF14)>, /* LCD_R7 */
				 <STM32_PINMUX('J',  7, AF14)>, /* LCD_G0 */
				 <STM32_PINMUX('J',  8, AF14)>, /* LCD_G1 */
				 <STM32_PINMUX('J',  9, AF14)>, /* LCD_G2 */
				 <STM32_PINMUX('J', 10, AF14)>, /* LCD_G3 */
				 <STM32_PINMUX('J', 11, AF14)>, /* LCD_G4 */
				 <STM32_PINMUX('K',  0, AF14)>, /* LCD_G5 */
				 <STM32_PINMUX('K',  1, AF14)>, /* LCD_G6 */
				 <STM32_PINMUX('K',  2, AF14)>, /* LCD_G7 */
				 <STM32_PINMUX('J', 12, AF14)>, /* LCD_B0 */
				 <STM32_PINMUX('J', 13, AF14)>, /* LCD_B1 */
				 <STM32_PINMUX('J', 14, AF14)>, /* LCD_B2 */
				 <STM32_PINMUX('J', 15, AF14)>, /* LCD_B3 */
				 <STM32_PINMUX('K',  3, AF14)>, /* LCD_B4 */
				 <STM32_PINMUX('K',  4, AF14)>, /* LCD_B5 */
				 <STM32_PINMUX('K',  5, AF14)>, /* LCD_B6 */
				 <STM32_PINMUX('K',  6, AF14)>; /* LCD_B7 */
			bias-disable;
			drive-push-pull;
			slew-rate = <1>;
		};
	};

	ltdc_sleep_pins_b: ltdc-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('I', 14, ANALOG)>, /* LCD_CLK */
				 <STM32_PINMUX('I', 12, ANALOG)>, /* LCD_HSYNC */
				 <STM32_PINMUX('I', 13, ANALOG)>, /* LCD_VSYNC */
				 <STM32_PINMUX('K',  7, ANALOG)>, /* LCD_DE */
				 <STM32_PINMUX('I', 15, ANALOG)>, /* LCD_R0 */
				 <STM32_PINMUX('J',  0, ANALOG)>, /* LCD_R1 */
				 <STM32_PINMUX('J',  1, ANALOG)>, /* LCD_R2 */
				 <STM32_PINMUX('J',  2, ANALOG)>, /* LCD_R3 */
				 <STM32_PINMUX('J',  3, ANALOG)>, /* LCD_R4 */
				 <STM32_PINMUX('J',  4, ANALOG)>, /* LCD_R5 */
				 <STM32_PINMUX('J',  5, ANALOG)>, /* LCD_R6 */
				 <STM32_PINMUX('J',  6, ANALOG)>, /* LCD_R7 */
				 <STM32_PINMUX('J',  7, ANALOG)>, /* LCD_G0 */
				 <STM32_PINMUX('J',  8, ANALOG)>, /* LCD_G1 */
				 <STM32_PINMUX('J',  9, ANALOG)>, /* LCD_G2 */
				 <STM32_PINMUX('J', 10, ANALOG)>, /* LCD_G3 */
				 <STM32_PINMUX('J', 11, ANALOG)>, /* LCD_G4 */
				 <STM32_PINMUX('K',  0, ANALOG)>, /* LCD_G5 */
				 <STM32_PINMUX('K',  1, ANALOG)>, /* LCD_G6 */
				 <STM32_PINMUX('K',  2, ANALOG)>, /* LCD_G7 */
				 <STM32_PINMUX('J', 12, ANALOG)>, /* LCD_B0 */
				 <STM32_PINMUX('J', 13, ANALOG)>, /* LCD_B1 */
				 <STM32_PINMUX('J', 14, ANALOG)>, /* LCD_B2 */
				 <STM32_PINMUX('J', 15, ANALOG)>, /* LCD_B3 */
				 <STM32_PINMUX('K',  3, ANALOG)>, /* LCD_B4 */
				 <STM32_PINMUX('K',  4, ANALOG)>, /* LCD_B5 */
				 <STM32_PINMUX('K',  5, ANALOG)>, /* LCD_B6 */
				 <STM32_PINMUX('K',  6, ANALOG)>; /* LCD_B7 */
		};
	};

	ltdc_pins_c: ltdc-2 {
		pins1 {
			pinmux = <STM32_PINMUX('B',  1, AF9)>,  /* LTDC_R6 */
				 <STM32_PINMUX('B',  9, AF14)>, /* LTDC_B7 */
				 <STM32_PINMUX('C',  0, AF14)>, /* LTDC_R5 */
				 <STM32_PINMUX('D',  3, AF14)>, /* LTDC_G7 */
				 <STM32_PINMUX('D',  6, AF14)>, /* LTDC_B2 */
				 <STM32_PINMUX('D', 10, AF14)>, /* LTDC_B3 */
				 <STM32_PINMUX('E', 11, AF14)>, /* LTDC_G3 */
				 <STM32_PINMUX('E', 12, AF14)>, /* LTDC_B4 */
				 <STM32_PINMUX('E', 13, AF14)>, /* LTDC_DE */
				 <STM32_PINMUX('E', 15, AF14)>, /* LTDC_R7 */
				 <STM32_PINMUX('H',  4, AF9)>,  /* LTDC_G5 */
				 <STM32_PINMUX('H',  8, AF14)>, /* LTDC_R2 */
				 <STM32_PINMUX('H',  9, AF14)>, /* LTDC_R3 */
				 <STM32_PINMUX('H', 10, AF14)>, /* LTDC_R4 */
				 <STM32_PINMUX('H', 13, AF14)>, /* LTDC_G2 */
				 <STM32_PINMUX('H', 15, AF14)>, /* LTDC_G4 */
				 <STM32_PINMUX('I',  1, AF14)>, /* LTDC_G6 */
				 <STM32_PINMUX('I',  5, AF14)>, /* LTDC_B5 */
				 <STM32_PINMUX('I',  6, AF14)>, /* LTDC_B6 */
				 <STM32_PINMUX('I',  9, AF14)>, /* LTDC_VSYNC */
				 <STM32_PINMUX('I', 10, AF14)>; /* LTDC_HSYNC */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('E', 14, AF14)>; /* LTDC_CLK */
			bias-disable;
			drive-push-pull;
			slew-rate = <1>;
		};
	};

	ltdc_sleep_pins_c: ltdc-sleep-2 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 1, ANALOG)>,  /* LTDC_R6 */
				 <STM32_PINMUX('B', 9, ANALOG)>, /* LTDC_B7 */
				 <STM32_PINMUX('C', 0, ANALOG)>, /* LTDC_R5 */
				 <STM32_PINMUX('D', 3, ANALOG)>, /* LTDC_G7 */
				 <STM32_PINMUX('D', 6, ANALOG)>, /* LTDC_B2 */
				 <STM32_PINMUX('D', 10, ANALOG)>, /* LTDC_B3 */
				 <STM32_PINMUX('E', 11, ANALOG)>, /* LTDC_G3 */
				 <STM32_PINMUX('E', 12, ANALOG)>, /* LTDC_B4 */
				 <STM32_PINMUX('E', 13, ANALOG)>, /* LTDC_DE */
				 <STM32_PINMUX('E', 15, ANALOG)>, /* LTDC_R7 */
				 <STM32_PINMUX('H', 4, ANALOG)>,  /* LTDC_G5 */
				 <STM32_PINMUX('H', 8, ANALOG)>, /* LTDC_R2 */
				 <STM32_PINMUX('H', 9, ANALOG)>, /* LTDC_R3 */
				 <STM32_PINMUX('H', 10, ANALOG)>, /* LTDC_R4 */
				 <STM32_PINMUX('H', 13, ANALOG)>, /* LTDC_G2 */
				 <STM32_PINMUX('H', 15, ANALOG)>, /* LTDC_G4 */
				 <STM32_PINMUX('I', 1, ANALOG)>, /* LTDC_G6 */
				 <STM32_PINMUX('I', 5, ANALOG)>, /* LTDC_B5 */
				 <STM32_PINMUX('I', 6, ANALOG)>, /* LTDC_B6 */
				 <STM32_PINMUX('I', 9, ANALOG)>, /* LTDC_VSYNC */
				 <STM32_PINMUX('I', 10, ANALOG)>, /* LTDC_HSYNC */
				 <STM32_PINMUX('E', 14, ANALOG)>; /* LTDC_CLK */
		};
	};

	ltdc_pins_d: ltdc-3 {
		pins1 {
			pinmux = <STM32_PINMUX('G',  7, AF14)>; /* LCD_CLK */
			bias-disable;
			drive-push-pull;
			slew-rate = <3>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('I', 10, AF14)>, /* LCD_HSYNC */
				 <STM32_PINMUX('I',  9, AF14)>, /* LCD_VSYNC */
				 <STM32_PINMUX('E', 13, AF14)>, /* LCD_DE */
				 <STM32_PINMUX('G', 13, AF14)>, /* LCD_R0 */
				 <STM32_PINMUX('H',  3, AF14)>, /* LCD_R1 */
				 <STM32_PINMUX('H',  8, AF14)>, /* LCD_R2 */
				 <STM32_PINMUX('H',  9, AF14)>, /* LCD_R3 */
				 <STM32_PINMUX('A',  5, AF14)>, /* LCD_R4 */
				 <STM32_PINMUX('H', 11, AF14)>, /* LCD_R5 */
				 <STM32_PINMUX('H', 12, AF14)>, /* LCD_R6 */
				 <STM32_PINMUX('E', 15, AF14)>, /* LCD_R7 */
				 <STM32_PINMUX('E',  5, AF14)>, /* LCD_G0 */
				 <STM32_PINMUX('B',  0, AF14)>, /* LCD_G1 */
				 <STM32_PINMUX('H', 13, AF14)>, /* LCD_G2 */
				 <STM32_PINMUX('E', 11, AF14)>, /* LCD_G3 */
				 <STM32_PINMUX('H', 15, AF14)>, /* LCD_G4 */
				 <STM32_PINMUX('H',  4,  AF9)>, /* LCD_G5 */
				 <STM32_PINMUX('I', 11,  AF9)>, /* LCD_G6 */
				 <STM32_PINMUX('G',  8, AF14)>, /* LCD_G7 */
				 <STM32_PINMUX('D',  9, AF14)>, /* LCD_B0 */
				 <STM32_PINMUX('G', 12, AF14)>, /* LCD_B1 */
				 <STM32_PINMUX('G', 10, AF14)>, /* LCD_B2 */
				 <STM32_PINMUX('D', 10, AF14)>, /* LCD_B3 */
				 <STM32_PINMUX('E', 12, AF14)>, /* LCD_B4 */
				 <STM32_PINMUX('A',  3, AF14)>, /* LCD_B5 */
				 <STM32_PINMUX('B',  8, AF14)>, /* LCD_B6 */
				 <STM32_PINMUX('I',  7, AF14)>; /* LCD_B7 */
			bias-disable;
			drive-push-pull;
			slew-rate = <2>;
		};
	};

	ltdc_sleep_pins_d: ltdc-sleep-3 {
		pins {
			pinmux = <STM32_PINMUX('G',  7, ANALOG)>, /* LCD_CLK */
				 <STM32_PINMUX('I', 10, ANALOG)>, /* LCD_HSYNC */
				 <STM32_PINMUX('I',  9, ANALOG)>, /* LCD_VSYNC */
				 <STM32_PINMUX('E', 13, ANALOG)>, /* LCD_DE */
				 <STM32_PINMUX('G', 13, ANALOG)>, /* LCD_R0 */
				 <STM32_PINMUX('H',  3, ANALOG)>, /* LCD_R1 */
				 <STM32_PINMUX('H',  8, ANALOG)>, /* LCD_R2 */
				 <STM32_PINMUX('H',  9, ANALOG)>, /* LCD_R3 */
				 <STM32_PINMUX('A',  5, ANALOG)>, /* LCD_R4 */
				 <STM32_PINMUX('H', 11, ANALOG)>, /* LCD_R5 */
				 <STM32_PINMUX('H', 12, ANALOG)>, /* LCD_R6 */
				 <STM32_PINMUX('E', 15, ANALOG)>, /* LCD_R7 */
				 <STM32_PINMUX('E',  5, ANALOG)>, /* LCD_G0 */
				 <STM32_PINMUX('B',  0, ANALOG)>, /* LCD_G1 */
				 <STM32_PINMUX('H', 13, ANALOG)>, /* LCD_G2 */
				 <STM32_PINMUX('E', 11, ANALOG)>, /* LCD_G3 */
				 <STM32_PINMUX('H', 15, ANALOG)>, /* LCD_G4 */
				 <STM32_PINMUX('H',  4, ANALOG)>, /* LCD_G5 */
				 <STM32_PINMUX('I', 11, ANALOG)>, /* LCD_G6 */
				 <STM32_PINMUX('G',  8, ANALOG)>, /* LCD_G7 */
				 <STM32_PINMUX('D',  9, ANALOG)>, /* LCD_B0 */
				 <STM32_PINMUX('G', 12, ANALOG)>, /* LCD_B1 */
				 <STM32_PINMUX('G', 10, ANALOG)>, /* LCD_B2 */
				 <STM32_PINMUX('D', 10, ANALOG)>, /* LCD_B3 */
				 <STM32_PINMUX('E', 12, ANALOG)>, /* LCD_B4 */
				 <STM32_PINMUX('A',  3, ANALOG)>, /* LCD_B5 */
				 <STM32_PINMUX('B',  8, ANALOG)>, /* LCD_B6 */
				 <STM32_PINMUX('I',  7, ANALOG)>; /* LCD_B7 */
		};
	};

	mco2_pins_a: mco2-0 {
		pins {
			pinmux = <STM32_PINMUX('G', 2, AF1)>; /* MCO2 */
			bias-disable;
			drive-push-pull;
			slew-rate = <2>;
		};
	};

	mco2_sleep_pins_a: mco2-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('G', 2, ANALOG)>; /* MCO2 */
		};
	};

	m_can1_pins_a: m-can1-0 {
		pins1 {
			pinmux = <STM32_PINMUX('H', 13, AF9)>; /* CAN1_TX */
			slew-rate = <1>;
			drive-push-pull;
			bias-disable;
		};
		pins2 {
			pinmux = <STM32_PINMUX('I', 9, AF9)>; /* CAN1_RX */
			bias-disable;
		};
	};

	m_can1_sleep_pins_a: m_can1-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('H', 13, ANALOG)>, /* CAN1_TX */
				 <STM32_PINMUX('I', 9, ANALOG)>; /* CAN1_RX */
		};
	};

	m_can1_pins_b: m-can1-1 {
		pins1 {
			pinmux = <STM32_PINMUX('A', 12, AF9)>; /* CAN1_TX */
			slew-rate = <1>;
			drive-push-pull;
			bias-disable;
		};
		pins2 {
			pinmux = <STM32_PINMUX('A', 11, AF9)>; /* CAN1_RX */
			bias-disable;
		};
	};

	m_can1_sleep_pins_b: m_can1-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('A', 12, ANALOG)>, /* CAN1_TX */
				 <STM32_PINMUX('A', 11, ANALOG)>; /* CAN1_RX */
		};
	};

	m_can2_pins_a: m-can2-0 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 13, AF9)>; /* CAN2_TX */
			slew-rate = <1>;
			drive-push-pull;
			bias-disable;
		};
		pins2 {
			pinmux = <STM32_PINMUX('B', 5, AF9)>; /* CAN2_RX */
			bias-disable;
		};
	};

	m_can2_sleep_pins_a: m_can2-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('B', 13, ANALOG)>, /* CAN2_TX */
				 <STM32_PINMUX('B', 5, ANALOG)>; /* CAN2_RX */
		};
	};

	pwm1_pins_a: pwm1-0 {
		pins {
			pinmux = <STM32_PINMUX('E', 9, AF1)>, /* TIM1_CH1 */
				 <STM32_PINMUX('E', 11, AF1)>, /* TIM1_CH2 */
				 <STM32_PINMUX('E', 14, AF1)>; /* TIM1_CH4 */
			bias-pull-down;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm1_sleep_pins_a: pwm1-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('E', 9, ANALOG)>, /* TIM1_CH1 */
				 <STM32_PINMUX('E', 11, ANALOG)>, /* TIM1_CH2 */
				 <STM32_PINMUX('E', 14, ANALOG)>; /* TIM1_CH4 */
		};
	};

	pwm1_pins_b: pwm1-1 {
		pins {
			pinmux = <STM32_PINMUX('E', 9, AF1)>; /* TIM1_CH1 */
			bias-pull-down;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm1_sleep_pins_b: pwm1-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('E', 9, ANALOG)>; /* TIM1_CH1 */
		};
	};

	pwm2_pins_a: pwm2-0 {
		pins {
			pinmux = <STM32_PINMUX('A', 3, AF1)>; /* TIM2_CH4 */
			bias-pull-down;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm2_sleep_pins_a: pwm2-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('A', 3, ANALOG)>; /* TIM2_CH4 */
		};
	};

	pwm3_pins_a: pwm3-0 {
		pins {
			pinmux = <STM32_PINMUX('C', 7, AF2)>; /* TIM3_CH2 */
			bias-pull-down;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm3_sleep_pins_a: pwm3-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('C', 7, ANALOG)>; /* TIM3_CH2 */
		};
	};

	pwm3_pins_b: pwm3-1 {
		pins {
			pinmux = <STM32_PINMUX('B', 5, AF2)>; /* TIM3_CH2 */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm3_sleep_pins_b: pwm3-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('B', 5, ANALOG)>; /* TIM3_CH2 */
		};
	};

	pwm4_pins_a: pwm4-0 {
		pins {
			pinmux = <STM32_PINMUX('D', 14, AF2)>, /* TIM4_CH3 */
				 <STM32_PINMUX('D', 15, AF2)>; /* TIM4_CH4 */
			bias-pull-down;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm4_sleep_pins_a: pwm4-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('D', 14, ANALOG)>, /* TIM4_CH3 */
				 <STM32_PINMUX('D', 15, ANALOG)>; /* TIM4_CH4 */
		};
	};

	pwm4_pins_b: pwm4-1 {
		pins {
			pinmux = <STM32_PINMUX('D', 13, AF2)>; /* TIM4_CH2 */
			bias-pull-down;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm4_sleep_pins_b: pwm4-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('D', 13, ANALOG)>; /* TIM4_CH2 */
		};
	};

	pwm5_pins_a: pwm5-0 {
		pins {
			pinmux = <STM32_PINMUX('H', 11, AF2)>; /* TIM5_CH2 */
			bias-pull-down;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm5_sleep_pins_a: pwm5-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('H', 11, ANALOG)>; /* TIM5_CH2 */
		};
	};

	pwm5_pins_b: pwm5-1 {
		pins {
			pinmux = <STM32_PINMUX('H', 11, AF2)>, /* TIM5_CH2 */
				 <STM32_PINMUX('H', 12, AF2)>, /* TIM5_CH3 */
				 <STM32_PINMUX('I', 0, AF2)>; /* TIM5_CH4 */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm5_sleep_pins_b: pwm5-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('H', 11, ANALOG)>, /* TIM5_CH2 */
				 <STM32_PINMUX('H', 12, ANALOG)>, /* TIM5_CH3 */
				 <STM32_PINMUX('I', 0, ANALOG)>; /* TIM5_CH4 */
		};
	};

	pwm8_pins_a: pwm8-0 {
		pins {
			pinmux = <STM32_PINMUX('I', 2, AF3)>; /* TIM8_CH4 */
			bias-pull-down;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm8_sleep_pins_a: pwm8-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('I', 2, ANALOG)>; /* TIM8_CH4 */
		};
	};

	pwm12_pins_a: pwm12-0 {
		pins {
			pinmux = <STM32_PINMUX('H', 6, AF2)>; /* TIM12_CH1 */
			bias-pull-down;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm12_sleep_pins_a: pwm12-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('H', 6, ANALOG)>; /* TIM12_CH1 */
		};
	};

	qspi_clk_pins_a: qspi-clk-0 {
		pins {
			pinmux = <STM32_PINMUX('F', 10, AF9)>; /* QSPI_CLK */
			bias-disable;
			drive-push-pull;
			slew-rate = <3>;
		};
	};

	qspi_clk_sleep_pins_a: qspi-clk-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('F', 10, ANALOG)>; /* QSPI_CLK */
		};
	};

	qspi_bk1_pins_a: qspi-bk1-0 {
		pins1 {
			pinmux = <STM32_PINMUX('F', 8, AF10)>, /* QSPI_BK1_IO0 */
				 <STM32_PINMUX('F', 9, AF10)>, /* QSPI_BK1_IO1 */
				 <STM32_PINMUX('F', 7, AF9)>, /* QSPI_BK1_IO2 */
				 <STM32_PINMUX('F', 6, AF9)>; /* QSPI_BK1_IO3 */
			bias-disable;
			drive-push-pull;
			slew-rate = <1>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('B', 6, AF10)>; /* QSPI_BK1_NCS */
			bias-pull-up;
			drive-push-pull;
			slew-rate = <1>;
		};
	};

	qspi_bk1_sleep_pins_a: qspi-bk1-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('F', 8, ANALOG)>, /* QSPI_BK1_IO0 */
				 <STM32_PINMUX('F', 9, ANALOG)>, /* QSPI_BK1_IO1 */
				 <STM32_PINMUX('F', 7, ANALOG)>, /* QSPI_BK1_IO2 */
				 <STM32_PINMUX('F', 6, ANALOG)>, /* QSPI_BK1_IO3 */
				 <STM32_PINMUX('B', 6, ANALOG)>; /* QSPI_BK1_NCS */
		};
	};

	qspi_bk2_pins_a: qspi-bk2-0 {
		pins1 {
			pinmux = <STM32_PINMUX('H', 2, AF9)>, /* QSPI_BK2_IO0 */
				 <STM32_PINMUX('H', 3, AF9)>, /* QSPI_BK2_IO1 */
				 <STM32_PINMUX('G', 10, AF11)>, /* QSPI_BK2_IO2 */
				 <STM32_PINMUX('G', 7, AF11)>; /* QSPI_BK2_IO3 */
			bias-disable;
			drive-push-pull;
			slew-rate = <1>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('C', 0, AF10)>; /* QSPI_BK2_NCS */
			bias-pull-up;
			drive-push-pull;
			slew-rate = <1>;
		};
	};

	qspi_bk2_sleep_pins_a: qspi-bk2-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('H', 2, ANALOG)>, /* QSPI_BK2_IO0 */
				 <STM32_PINMUX('H', 3, ANALOG)>, /* QSPI_BK2_IO1 */
				 <STM32_PINMUX('G', 10, ANALOG)>, /* QSPI_BK2_IO2 */
				 <STM32_PINMUX('G', 7, ANALOG)>, /* QSPI_BK2_IO3 */
				 <STM32_PINMUX('C', 0, ANALOG)>; /* QSPI_BK2_NCS */
		};
	};

	sai2a_pins_a: sai2a-0 {
		pins {
			pinmux = <STM32_PINMUX('I', 5, AF10)>, /* SAI2_SCK_A */
				 <STM32_PINMUX('I', 6, AF10)>, /* SAI2_SD_A */
				 <STM32_PINMUX('I', 7, AF10)>, /* SAI2_FS_A */
				 <STM32_PINMUX('E', 0, AF10)>; /* SAI2_MCLK_A */
			slew-rate = <0>;
			drive-push-pull;
			bias-disable;
		};
	};

	sai2a_sleep_pins_a: sai2a-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('I', 5, ANALOG)>, /* SAI2_SCK_A */
				 <STM32_PINMUX('I', 6, ANALOG)>, /* SAI2_SD_A */
				 <STM32_PINMUX('I', 7, ANALOG)>, /* SAI2_FS_A */
				 <STM32_PINMUX('E', 0, ANALOG)>; /* SAI2_MCLK_A */
		};
	};

	sai2a_pins_b: sai2a-1 {
		pins1 {
			pinmux = <STM32_PINMUX('I', 6, AF10)>,	/* SAI2_SD_A */
				 <STM32_PINMUX('I', 7, AF10)>,	/* SAI2_FS_A */
				 <STM32_PINMUX('D', 13, AF10)>;	/* SAI2_SCK_A */
			slew-rate = <0>;
			drive-push-pull;
			bias-disable;
		};
	};

	sai2a_sleep_pins_b: sai2a-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('I', 6, ANALOG)>,  /* SAI2_SD_A */
				 <STM32_PINMUX('I', 7, ANALOG)>,  /* SAI2_FS_A */
				 <STM32_PINMUX('D', 13, ANALOG)>; /* SAI2_SCK_A */
		};
	};

	sai2a_pins_c: sai2a-2 {
		pins {
			pinmux = <STM32_PINMUX('D', 13, AF10)>, /* SAI2_SCK_A */
				 <STM32_PINMUX('D', 11, AF10)>, /* SAI2_SD_A */
				 <STM32_PINMUX('D', 12, AF10)>; /* SAI2_FS_A */
			slew-rate = <0>;
			drive-push-pull;
			bias-disable;
		};
	};

	sai2a_sleep_pins_c: sai2a-sleep-2 {
		pins {
			pinmux = <STM32_PINMUX('D', 13, ANALOG)>, /* SAI2_SCK_A */
				 <STM32_PINMUX('D', 11, ANALOG)>, /* SAI2_SD_A */
				 <STM32_PINMUX('D', 12, ANALOG)>; /* SAI2_FS_A */
		};
	};

	sai2b_pins_a: sai2b-0 {
		pins1 {
			pinmux = <STM32_PINMUX('E', 12, AF10)>, /* SAI2_SCK_B */
				 <STM32_PINMUX('E', 13, AF10)>, /* SAI2_FS_B */
				 <STM32_PINMUX('E', 14, AF10)>; /* SAI2_MCLK_B */
			slew-rate = <0>;
			drive-push-pull;
			bias-disable;
		};
		pins2 {
			pinmux = <STM32_PINMUX('F', 11, AF10)>; /* SAI2_SD_B */
			bias-disable;
		};
	};

	sai2b_sleep_pins_a: sai2b-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('F', 11, ANALOG)>, /* SAI2_SD_B */
				 <STM32_PINMUX('E', 12, ANALOG)>, /* SAI2_SCK_B */
				 <STM32_PINMUX('E', 13, ANALOG)>, /* SAI2_FS_B */
				 <STM32_PINMUX('E', 14, ANALOG)>; /* SAI2_MCLK_B */
		};
	};

	sai2b_pins_b: sai2b-1 {
		pins {
			pinmux = <STM32_PINMUX('F', 11, AF10)>; /* SAI2_SD_B */
			bias-disable;
		};
	};

	sai2b_sleep_pins_b: sai2b-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('F', 11, ANALOG)>; /* SAI2_SD_B */
		};
	};

	sai2b_pins_c: sai2b-2 {
		pins1 {
			pinmux = <STM32_PINMUX('F', 11, AF10)>; /* SAI2_SD_B */
			bias-disable;
		};
	};

	sai2b_sleep_pins_c: sai2b-sleep-2 {
		pins {
			pinmux = <STM32_PINMUX('F', 11, ANALOG)>; /* SAI2_SD_B */
		};
	};

	sai4a_pins_a: sai4a-0 {
		pins {
			pinmux = <STM32_PINMUX('B', 5, AF10)>; /* SAI4_SD_A */
			slew-rate = <0>;
			drive-push-pull;
			bias-disable;
		};
	};

	sai4a_sleep_pins_a: sai4a-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('B', 5, ANALOG)>; /* SAI4_SD_A */
		};
	};

	sdmmc1_b4_pins_a: sdmmc1-b4-0 {
		pins1 {
			pinmux = <STM32_PINMUX('C', 8, AF12)>, /* SDMMC1_D0 */
				 <STM32_PINMUX('C', 9, AF12)>, /* SDMMC1_D1 */
				 <STM32_PINMUX('C', 10, AF12)>, /* SDMMC1_D2 */
				 <STM32_PINMUX('C', 11, AF12)>, /* SDMMC1_D3 */
				 <STM32_PINMUX('D', 2, AF12)>; /* SDMMC1_CMD */
			slew-rate = <1>;
			drive-push-pull;
			bias-disable;
		};
		pins2 {
			pinmux = <STM32_PINMUX('C', 12, AF12)>; /* SDMMC1_CK */
			slew-rate = <2>;
			drive-push-pull;
			bias-disable;
		};
	};

	sdmmc1_b4_od_pins_a: sdmmc1-b4-od-0 {
		pins1 {
			pinmux = <STM32_PINMUX('C', 8, AF12)>, /* SDMMC1_D0 */
				 <STM32_PINMUX('C', 9, AF12)>, /* SDMMC1_D1 */
				 <STM32_PINMUX('C', 10, AF12)>, /* SDMMC1_D2 */
				 <STM32_PINMUX('C', 11, AF12)>; /* SDMMC1_D3 */
			slew-rate = <1>;
			drive-push-pull;
			bias-disable;
		};
		pins2 {
			pinmux = <STM32_PINMUX('C', 12, AF12)>; /* SDMMC1_CK */
			slew-rate = <2>;
			drive-push-pull;
			bias-disable;
		};
		pins3 {
			pinmux = <STM32_PINMUX('D', 2, AF12)>; /* SDMMC1_CMD */
			slew-rate = <1>;
			drive-open-drain;
			bias-disable;
		};
	};

	sdmmc1_b4_init_pins_a: sdmmc1-b4-init-0 {
		pins1 {
			pinmux = <STM32_PINMUX('C', 8, AF12)>, /* SDMMC1_D0 */
				 <STM32_PINMUX('C', 9, AF12)>, /* SDMMC1_D1 */
				 <STM32_PINMUX('C', 10, AF12)>, /* SDMMC1_D2 */
				 <STM32_PINMUX('C', 11, AF12)>; /* SDMMC1_D3 */
			slew-rate = <1>;
			drive-push-pull;
			bias-disable;
		};
	};

	sdmmc1_b4_sleep_pins_a: sdmmc1-b4-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('C', 8, ANALOG)>, /* SDMMC1_D0 */
				 <STM32_PINMUX('C', 9, ANALOG)>, /* SDMMC1_D1 */
				 <STM32_PINMUX('C', 10, ANALOG)>, /* SDMMC1_D2 */
				 <STM32_PINMUX('C', 11, ANALOG)>, /* SDMMC1_D3 */
				 <STM32_PINMUX('C', 12, ANALOG)>, /* SDMMC1_CK */
				 <STM32_PINMUX('D', 2, ANALOG)>; /* SDMMC1_CMD */
		};
	};

	sdmmc1_dir_pins_a: sdmmc1-dir-0 {
		pins1 {
			pinmux = <STM32_PINMUX('F', 2, AF11)>, /* SDMMC1_D0DIR */
				 <STM32_PINMUX('C', 7, AF8)>, /* SDMMC1_D123DIR */
				 <STM32_PINMUX('B', 9, AF11)>; /* SDMMC1_CDIR */
			slew-rate = <1>;
			drive-push-pull;
			bias-pull-up;
		};
		pins2{
			pinmux = <STM32_PINMUX('E', 4, AF8)>; /* SDMMC1_CKIN */
			bias-pull-up;
		};
	};

	sdmmc1_dir_init_pins_a: sdmmc1-dir-init-0 {
		pins1 {
			pinmux = <STM32_PINMUX('F', 2, AF11)>, /* SDMMC1_D0DIR */
				 <STM32_PINMUX('C', 7, AF8)>, /* SDMMC1_D123DIR */
				 <STM32_PINMUX('B', 9, AF11)>; /* SDMMC1_CDIR */
			slew-rate = <1>;
			drive-push-pull;
			bias-pull-up;
		};
	};

	sdmmc1_dir_sleep_pins_a: sdmmc1-dir-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('F', 2, ANALOG)>, /* SDMMC1_D0DIR */
				 <STM32_PINMUX('C', 7, ANALOG)>, /* SDMMC1_D123DIR */
				 <STM32_PINMUX('B', 9, ANALOG)>, /* SDMMC1_CDIR */
				 <STM32_PINMUX('E', 4, ANALOG)>; /* SDMMC1_CKIN */
		};
	};

	sdmmc1_dir_pins_b: sdmmc1-dir-1 {
		pins1 {
			pinmux = <STM32_PINMUX('F', 2, AF11)>, /* SDMMC1_D0DIR */
				 <STM32_PINMUX('E', 14, AF11)>, /* SDMMC1_D123DIR */
				 <STM32_PINMUX('B', 9, AF11)>; /* SDMMC1_CDIR */
			slew-rate = <1>;
			drive-push-pull;
			bias-pull-up;
		};
		pins2{
			pinmux = <STM32_PINMUX('E', 4, AF8)>; /* SDMMC1_CKIN */
			bias-pull-up;
		};
	};

	sdmmc1_dir_sleep_pins_b: sdmmc1-dir-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('F', 2, ANALOG)>, /* SDMMC1_D0DIR */
				 <STM32_PINMUX('E', 14, ANALOG)>, /* SDMMC1_D123DIR */
				 <STM32_PINMUX('B', 9, ANALOG)>, /* SDMMC1_CDIR */
				 <STM32_PINMUX('E', 4, ANALOG)>; /* SDMMC1_CKIN */
		};
	};

	sdmmc2_b4_pins_a: sdmmc2-b4-0 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 14, AF9)>, /* SDMMC2_D0 */
				 <STM32_PINMUX('B', 15, AF9)>, /* SDMMC2_D1 */
				 <STM32_PINMUX('B', 3, AF9)>, /* SDMMC2_D2 */
				 <STM32_PINMUX('B', 4, AF9)>, /* SDMMC2_D3 */
				 <STM32_PINMUX('G', 6, AF10)>; /* SDMMC2_CMD */
			slew-rate = <1>;
			drive-push-pull;
			bias-pull-up;
		};
		pins2 {
			pinmux = <STM32_PINMUX('E', 3, AF9)>; /* SDMMC2_CK */
			slew-rate = <2>;
			drive-push-pull;
			bias-pull-up;
		};
	};

	sdmmc2_b4_od_pins_a: sdmmc2-b4-od-0 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 14, AF9)>, /* SDMMC2_D0 */
				 <STM32_PINMUX('B', 15, AF9)>, /* SDMMC2_D1 */
				 <STM32_PINMUX('B', 3, AF9)>, /* SDMMC2_D2 */
				 <STM32_PINMUX('B', 4, AF9)>; /* SDMMC2_D3 */
			slew-rate = <1>;
			drive-push-pull;
			bias-pull-up;
		};
		pins2 {
			pinmux = <STM32_PINMUX('E', 3, AF9)>; /* SDMMC2_CK */
			slew-rate = <2>;
			drive-push-pull;
			bias-pull-up;
		};
		pins3 {
			pinmux = <STM32_PINMUX('G', 6, AF10)>; /* SDMMC2_CMD */
			slew-rate = <1>;
			drive-open-drain;
			bias-pull-up;
		};
	};

	sdmmc2_b4_sleep_pins_a: sdmmc2-b4-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('B', 14, ANALOG)>, /* SDMMC2_D0 */
				 <STM32_PINMUX('B', 15, ANALOG)>, /* SDMMC2_D1 */
				 <STM32_PINMUX('B', 3, ANALOG)>, /* SDMMC2_D2 */
				 <STM32_PINMUX('B', 4, ANALOG)>, /* SDMMC2_D3 */
				 <STM32_PINMUX('E', 3, ANALOG)>, /* SDMMC2_CK */
				 <STM32_PINMUX('G', 6, ANALOG)>; /* SDMMC2_CMD */
		};
	};

	sdmmc2_b4_pins_b: sdmmc2-b4-1 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 14, AF9)>, /* SDMMC2_D0 */
				 <STM32_PINMUX('B', 15, AF9)>, /* SDMMC2_D1 */
				 <STM32_PINMUX('B', 3, AF9)>, /* SDMMC2_D2 */
				 <STM32_PINMUX('B', 4, AF9)>, /* SDMMC2_D3 */
				 <STM32_PINMUX('G', 6, AF10)>; /* SDMMC2_CMD */
			slew-rate = <1>;
			drive-push-pull;
			bias-disable;
		};
		pins2 {
			pinmux = <STM32_PINMUX('E', 3, AF9)>; /* SDMMC2_CK */
			slew-rate = <2>;
			drive-push-pull;
			bias-disable;
		};
	};

	sdmmc2_b4_od_pins_b: sdmmc2-b4-od-1 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 14, AF9)>, /* SDMMC2_D0 */
				 <STM32_PINMUX('B', 15, AF9)>, /* SDMMC2_D1 */
				 <STM32_PINMUX('B', 3, AF9)>, /* SDMMC2_D2 */
				 <STM32_PINMUX('B', 4, AF9)>; /* SDMMC2_D3 */
			slew-rate = <1>;
			drive-push-pull;
			bias-disable;
		};
		pins2 {
			pinmux = <STM32_PINMUX('E', 3, AF9)>; /* SDMMC2_CK */
			slew-rate = <2>;
			drive-push-pull;
			bias-disable;
		};
		pins3 {
			pinmux = <STM32_PINMUX('G', 6, AF10)>; /* SDMMC2_CMD */
			slew-rate = <1>;
			drive-open-drain;
			bias-disable;
		};
	};

	sdmmc2_d47_pins_a: sdmmc2-d47-0 {
		pins {
			pinmux = <STM32_PINMUX('A', 8, AF9)>, /* SDMMC2_D4 */
				 <STM32_PINMUX('A', 9, AF10)>, /* SDMMC2_D5 */
				 <STM32_PINMUX('E', 5, AF9)>, /* SDMMC2_D6 */
				 <STM32_PINMUX('D', 3, AF9)>; /* SDMMC2_D7 */
			slew-rate = <1>;
			drive-push-pull;
			bias-pull-up;
		};
	};

	sdmmc2_d47_sleep_pins_a: sdmmc2-d47-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('A', 8, ANALOG)>, /* SDMMC2_D4 */
				 <STM32_PINMUX('A', 9, ANALOG)>, /* SDMMC2_D5 */
				 <STM32_PINMUX('E', 5, ANALOG)>, /* SDMMC2_D6 */
				 <STM32_PINMUX('D', 3, ANALOG)>; /* SDMMC2_D7 */
		};
	};

	sdmmc2_d47_pins_b: sdmmc2-d47-1 {
		pins {
			pinmux = <STM32_PINMUX('A', 8, AF9)>,  /* SDMMC2_D4 */
				 <STM32_PINMUX('A', 9, AF10)>, /* SDMMC2_D5 */
				 <STM32_PINMUX('C', 6, AF10)>, /* SDMMC2_D6 */
				 <STM32_PINMUX('C', 7, AF10)>; /* SDMMC2_D7 */
			slew-rate = <1>;
			drive-push-pull;
			bias-disable;
		};
	};

	sdmmc2_d47_sleep_pins_b: sdmmc2-d47-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('A', 8, ANALOG)>, /* SDMMC2_D4 */
				 <STM32_PINMUX('A', 9, ANALOG)>, /* SDMMC2_D5 */
				 <STM32_PINMUX('C', 6, ANALOG)>, /* SDMMC2_D6 */
				 <STM32_PINMUX('C', 7, ANALOG)>; /* SDMMC2_D7 */
		};
	};

	sdmmc2_d47_pins_c: sdmmc2-d47-2 {
		pins {
			pinmux = <STM32_PINMUX('A', 8, AF9)>, /* SDMMC2_D4 */
				 <STM32_PINMUX('A', 15, AF9)>, /* SDMMC2_D5 */
				 <STM32_PINMUX('C', 6, AF10)>, /* SDMMC2_D6 */
				 <STM32_PINMUX('C', 7, AF10)>; /* SDMMC2_D7 */
			slew-rate = <1>;
			drive-push-pull;
			bias-pull-up;
		};
	};

	sdmmc2_d47_sleep_pins_c: sdmmc2-d47-sleep-2 {
		pins {
			pinmux = <STM32_PINMUX('A', 8, ANALOG)>, /* SDMMC2_D4 */
				 <STM32_PINMUX('A', 15, ANALOG)>, /* SDMMC2_D5 */
				 <STM32_PINMUX('C', 6, ANALOG)>, /* SDMMC2_D6 */
				 <STM32_PINMUX('C', 7, ANALOG)>; /* SDMMC2_D7 */
		};
	};

	sdmmc2_d47_pins_d: sdmmc2-d47-3 {
		pins {
			pinmux = <STM32_PINMUX('A', 8, AF9)>, /* SDMMC2_D4 */
				 <STM32_PINMUX('A', 9, AF10)>, /* SDMMC2_D5 */
				 <STM32_PINMUX('E', 5, AF9)>, /* SDMMC2_D6 */
				 <STM32_PINMUX('C', 7, AF10)>; /* SDMMC2_D7 */
		};
	};

	sdmmc2_d47_sleep_pins_d: sdmmc2-d47-sleep-3 {
		pins {
			pinmux = <STM32_PINMUX('A', 8, ANALOG)>, /* SDMMC2_D4 */
				 <STM32_PINMUX('A', 9, ANALOG)>, /* SDMMC2_D5 */
				 <STM32_PINMUX('E', 5, ANALOG)>, /* SDMMC2_D6 */
				 <STM32_PINMUX('C', 7, ANALOG)>; /* SDMMC2_D7 */
		};
	};

	sdmmc3_b4_pins_a: sdmmc3-b4-0 {
		pins1 {
			pinmux = <STM32_PINMUX('F', 0, AF9)>, /* SDMMC3_D0 */
				 <STM32_PINMUX('F', 4, AF9)>, /* SDMMC3_D1 */
				 <STM32_PINMUX('F', 5, AF9)>, /* SDMMC3_D2 */
				 <STM32_PINMUX('D', 7, AF10)>, /* SDMMC3_D3 */
				 <STM32_PINMUX('F', 1, AF9)>; /* SDMMC3_CMD */
			slew-rate = <1>;
			drive-push-pull;
			bias-pull-up;
		};
		pins2 {
			pinmux = <STM32_PINMUX('G', 15, AF10)>; /* SDMMC3_CK */
			slew-rate = <2>;
			drive-push-pull;
			bias-pull-up;
		};
	};

	sdmmc3_b4_od_pins_a: sdmmc3-b4-od-0 {
		pins1 {
			pinmux = <STM32_PINMUX('F', 0, AF9)>, /* SDMMC3_D0 */
				 <STM32_PINMUX('F', 4, AF9)>, /* SDMMC3_D1 */
				 <STM32_PINMUX('F', 5, AF9)>, /* SDMMC3_D2 */
				 <STM32_PINMUX('D', 7, AF10)>; /* SDMMC3_D3 */
			slew-rate = <1>;
			drive-push-pull;
			bias-pull-up;
		};
		pins2 {
			pinmux = <STM32_PINMUX('G', 15, AF10)>; /* SDMMC3_CK */
			slew-rate = <2>;
			drive-push-pull;
			bias-pull-up;
		};
		pins3 {
			pinmux = <STM32_PINMUX('F', 1, AF9)>; /* SDMMC2_CMD */
			slew-rate = <1>;
			drive-open-drain;
			bias-pull-up;
		};
	};

	sdmmc3_b4_sleep_pins_a: sdmmc3-b4-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('F', 0, ANALOG)>, /* SDMMC3_D0 */
				 <STM32_PINMUX('F', 4, ANALOG)>, /* SDMMC3_D1 */
				 <STM32_PINMUX('F', 5, ANALOG)>, /* SDMMC3_D2 */
				 <STM32_PINMUX('D', 7, ANALOG)>, /* SDMMC3_D3 */
				 <STM32_PINMUX('G', 15, ANALOG)>, /* SDMMC3_CK */
				 <STM32_PINMUX('F', 1, ANALOG)>; /* SDMMC3_CMD */
		};
	};

	sdmmc3_b4_pins_b: sdmmc3-b4-1 {
		pins1 {
			pinmux = <STM32_PINMUX('F', 0, AF9)>, /* SDMMC3_D0 */
				 <STM32_PINMUX('F', 4, AF9)>, /* SDMMC3_D1 */
				 <STM32_PINMUX('D', 5, AF10)>, /* SDMMC3_D2 */
				 <STM32_PINMUX('D', 7, AF10)>, /* SDMMC3_D3 */
				 <STM32_PINMUX('D', 0, AF10)>; /* SDMMC3_CMD */
			slew-rate = <1>;
			drive-push-pull;
			bias-pull-up;
		};
		pins2 {
			pinmux = <STM32_PINMUX('G', 15, AF10)>; /* SDMMC3_CK */
			slew-rate = <2>;
			drive-push-pull;
			bias-pull-up;
		};
	};

	sdmmc3_b4_od_pins_b: sdmmc3-b4-od-1 {
		pins1 {
			pinmux = <STM32_PINMUX('F', 0, AF9)>, /* SDMMC3_D0 */
				 <STM32_PINMUX('F', 4, AF9)>, /* SDMMC3_D1 */
				 <STM32_PINMUX('D', 5, AF10)>, /* SDMMC3_D2 */
				 <STM32_PINMUX('D', 7, AF10)>; /* SDMMC3_D3 */
			slew-rate = <1>;
			drive-push-pull;
			bias-pull-up;
		};
		pins2 {
			pinmux = <STM32_PINMUX('G', 15, AF10)>; /* SDMMC3_CK */
			slew-rate = <2>;
			drive-push-pull;
			bias-pull-up;
		};
		pins3 {
			pinmux = <STM32_PINMUX('D', 0, AF10)>; /* SDMMC2_CMD */
			slew-rate = <1>;
			drive-open-drain;
			bias-pull-up;
		};
	};

	sdmmc3_b4_sleep_pins_b: sdmmc3-b4-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('F', 0, ANALOG)>, /* SDMMC3_D0 */
				 <STM32_PINMUX('F', 4, ANALOG)>, /* SDMMC3_D1 */
				 <STM32_PINMUX('D', 5, ANALOG)>, /* SDMMC3_D2 */
				 <STM32_PINMUX('D', 7, ANALOG)>, /* SDMMC3_D3 */
				 <STM32_PINMUX('G', 15, ANALOG)>, /* SDMMC3_CK */
				 <STM32_PINMUX('D', 0, ANALOG)>; /* SDMMC3_CMD */
		};
	};

	spdifrx_pins_a: spdifrx-0 {
		pins {
			pinmux = <STM32_PINMUX('G', 12, AF8)>; /* SPDIF_IN1 */
			bias-disable;
		};
	};

	spdifrx_sleep_pins_a: spdifrx-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('G', 12, ANALOG)>; /* SPDIF_IN1 */
		};
	};

	spi2_pins_a: spi2-0 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 10, AF5)>, /* SPI1_SCK */
				 <STM32_PINMUX('I', 3, AF5)>; /* SPI1_MOSI */
			bias-disable;
			drive-push-pull;
			slew-rate = <1>;
		};

		pins2 {
			pinmux = <STM32_PINMUX('I', 2, AF5)>; /* SPI1_MISO */
			bias-disable;
		};
	};

	spi4_pins_a: spi4-0 {
		pins {
			pinmux = <STM32_PINMUX('E', 12, AF5)>, /* SPI4_SCK */
				 <STM32_PINMUX('E', 6, AF5)>;  /* SPI4_MOSI */
			bias-disable;
			drive-push-pull;
			slew-rate = <1>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('E', 13, AF5)>; /* SPI4_MISO */
			bias-disable;
		};
	};

	stusb1600_pins_a: stusb1600-0 {
		pins {
			pinmux = <STM32_PINMUX('I', 11, GPIO)>;
			bias-pull-up;
		};
	};

	uart4_pins_a: uart4-0 {
		pins1 {
			pinmux = <STM32_PINMUX('G', 11, AF6)>; /* UART4_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('B', 2, AF8)>; /* UART4_RX */
			bias-disable;
		};
	};

	uart4_idle_pins_a: uart4-idle-0 {
		pins1 {
			pinmux = <STM32_PINMUX('G', 11, ANALOG)>; /* UART4_TX */
		};
		pins2 {
			pinmux = <STM32_PINMUX('B', 2, AF8)>; /* UART4_RX */
			bias-disable;
		};
	};

	uart4_sleep_pins_a: uart4-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('G', 11, ANALOG)>, /* UART4_TX */
				 <STM32_PINMUX('B', 2, ANALOG)>; /* UART4_RX */
		};
	};

	uart4_pins_b: uart4-1 {
		pins1 {
			pinmux = <STM32_PINMUX('D', 1, AF8)>; /* UART4_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('B', 2, AF8)>; /* UART4_RX */
			bias-disable;
		};
	};

	uart4_pins_c: uart4-2 {
		pins1 {
			pinmux = <STM32_PINMUX('G', 11, AF6)>; /* UART4_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('B', 2, AF8)>; /* UART4_RX */
			bias-disable;
		};
	};

	uart7_pins_a: uart7-0 {
		pins1 {
			pinmux = <STM32_PINMUX('E', 8, AF7)>; /* UART7_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('E', 7, AF7)>, /* UART7_RX */
				 <STM32_PINMUX('E', 10, AF7)>, /* UART7_CTS */
				 <STM32_PINMUX('E', 9, AF7)>; /* UART7_RTS */
			bias-disable;
		};
	};

	uart7_pins_b: uart7-1 {
		pins1 {
			pinmux = <STM32_PINMUX('F', 7, AF7)>; /* UART7_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('F', 6, AF7)>; /* UART7_RX */
			bias-disable;
		};
	};

	uart7_pins_c: uart7-2 {
		pins1 {
			pinmux = <STM32_PINMUX('E', 8, AF7)>; /* UART7_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('E', 7, AF7)>; /* UART7_RX */
			bias-pull-up;
		};
	};

	uart7_idle_pins_c: uart7-idle-2 {
		pins1 {
			pinmux = <STM32_PINMUX('E', 8, ANALOG)>; /* UART7_TX */
		};
		pins2 {
			pinmux = <STM32_PINMUX('E', 7, AF7)>; /* UART7_RX */
			bias-pull-up;
		};
	};

	uart7_sleep_pins_c: uart7-sleep-2 {
		pins {
			pinmux = <STM32_PINMUX('E', 8, ANALOG)>, /* UART7_TX */
				 <STM32_PINMUX('E', 7, ANALOG)>; /* UART7_RX */
		};
	};

	uart8_pins_a: uart8-0 {
		pins1 {
			pinmux = <STM32_PINMUX('E', 1, AF8)>; /* UART8_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('E', 0, AF8)>; /* UART8_RX */
			bias-disable;
		};
	};

	uart8_rtscts_pins_a: uart8rtscts-0 {
		pins {
			pinmux = <STM32_PINMUX('G', 7, AF8)>, /* UART8_RTS */
				 <STM32_PINMUX('G', 10, AF8)>; /* UART8_CTS */
			bias-disable;
		};
	};

	usart2_pins_a: usart2-0 {
		pins1 {
			pinmux = <STM32_PINMUX('F', 5, AF7)>, /* USART2_TX */
				 <STM32_PINMUX('D', 4, AF7)>; /* USART2_RTS */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 6, AF7)>, /* USART2_RX */
				 <STM32_PINMUX('D', 3, AF7)>; /* USART2_CTS_NSS */
			bias-disable;
		};
	};

	usart2_sleep_pins_a: usart2-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('F', 5, ANALOG)>, /* USART2_TX */
				 <STM32_PINMUX('D', 4, ANALOG)>, /* USART2_RTS */
				 <STM32_PINMUX('D', 6, ANALOG)>, /* USART2_RX */
				 <STM32_PINMUX('D', 3, ANALOG)>; /* USART2_CTS_NSS */
		};
	};

	usart2_pins_b: usart2-1 {
		pins1 {
			pinmux = <STM32_PINMUX('F', 5, AF7)>, /* USART2_TX */
				 <STM32_PINMUX('A', 1, AF7)>; /* USART2_RTS */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('F', 4, AF7)>, /* USART2_RX */
				 <STM32_PINMUX('E', 15, AF7)>; /* USART2_CTS_NSS */
			bias-disable;
		};
	};

	usart2_sleep_pins_b: usart2-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('F', 5, ANALOG)>, /* USART2_TX */
				 <STM32_PINMUX('A', 1, ANALOG)>, /* USART2_RTS */
				 <STM32_PINMUX('F', 4, ANALOG)>, /* USART2_RX */
				 <STM32_PINMUX('E', 15, ANALOG)>; /* USART2_CTS_NSS */
		};
	};

	usart2_pins_c: usart2-2 {
		pins1 {
			pinmux = <STM32_PINMUX('D', 5, AF7)>, /* USART2_TX */
				 <STM32_PINMUX('D', 4, AF7)>; /* USART2_RTS */
			bias-disable;
			drive-push-pull;
			slew-rate = <3>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 6, AF7)>, /* USART2_RX */
				 <STM32_PINMUX('D', 3, AF7)>; /* USART2_CTS_NSS */
			bias-disable;
		};
	};

	usart2_idle_pins_c: usart2-idle-2 {
		pins1 {
			pinmux = <STM32_PINMUX('D', 5, ANALOG)>, /* USART2_TX */
				 <STM32_PINMUX('D', 3, ANALOG)>; /* USART2_CTS_NSS */
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 4, AF7)>; /* USART2_RTS */
			bias-disable;
			drive-push-pull;
			slew-rate = <3>;
		};
		pins3 {
			pinmux = <STM32_PINMUX('D', 6, AF7)>; /* USART2_RX */
			bias-disable;
		};
	};

	usart2_sleep_pins_c: usart2-sleep-2 {
		pins {
			pinmux = <STM32_PINMUX('D', 5, ANALOG)>, /* USART2_TX */
				 <STM32_PINMUX('D', 4, ANALOG)>, /* USART2_RTS */
				 <STM32_PINMUX('D', 6, ANALOG)>, /* USART2_RX */
				 <STM32_PINMUX('D', 3, ANALOG)>; /* USART2_CTS_NSS */
		};
	};

	usart3_pins_a: usart3-0 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 10, AF7)>; /* USART3_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('B', 12, AF8)>; /* USART3_RX */
			bias-disable;
		};
	};

	usart3_pins_b: usart3-1 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 10, AF7)>, /* USART3_TX */
				 <STM32_PINMUX('G', 8, AF8)>; /* USART3_RTS */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('B', 12, AF8)>, /* USART3_RX */
				 <STM32_PINMUX('I', 10, AF8)>; /* USART3_CTS_NSS */
			bias-pull-up;
		};
	};

	usart3_idle_pins_b: usart3-idle-1 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 10, ANALOG)>, /* USART3_TX */
				 <STM32_PINMUX('I', 10, ANALOG)>; /* USART3_CTS_NSS */
		};
		pins2 {
			pinmux = <STM32_PINMUX('G', 8, AF8)>; /* USART3_RTS */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins3 {
			pinmux = <STM32_PINMUX('B', 12, AF8)>; /* USART3_RX */
			bias-pull-up;
		};
	};

	usart3_sleep_pins_b: usart3-sleep-1 {
		pins {
			pinmux = <STM32_PINMUX('B', 10, ANALOG)>, /* USART3_TX */
				 <STM32_PINMUX('G', 8, ANALOG)>, /* USART3_RTS */
				 <STM32_PINMUX('I', 10, ANALOG)>, /* USART3_CTS_NSS */
				 <STM32_PINMUX('B', 12, ANALOG)>; /* USART3_RX */
		};
	};

	usart3_pins_c: usart3-2 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 10, AF7)>, /* USART3_TX */
				 <STM32_PINMUX('G', 8, AF8)>; /* USART3_RTS */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('B', 12, AF8)>, /* USART3_RX */
				 <STM32_PINMUX('B', 13, AF7)>; /* USART3_CTS_NSS */
			bias-pull-up;
		};
	};

	usart3_idle_pins_c: usart3-idle-2 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 10, ANALOG)>, /* USART3_TX */
				 <STM32_PINMUX('B', 13, ANALOG)>; /* USART3_CTS_NSS */
		};
		pins2 {
			pinmux = <STM32_PINMUX('G', 8, AF8)>; /* USART3_RTS */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins3 {
			pinmux = <STM32_PINMUX('B', 12, AF8)>; /* USART3_RX */
			bias-pull-up;
		};
	};

	usart3_sleep_pins_c: usart3-sleep-2 {
		pins {
			pinmux = <STM32_PINMUX('B', 10, ANALOG)>, /* USART3_TX */
				 <STM32_PINMUX('G', 8, ANALOG)>, /* USART3_RTS */
				 <STM32_PINMUX('B', 13, ANALOG)>, /* USART3_CTS_NSS */
				 <STM32_PINMUX('B', 12, ANALOG)>; /* USART3_RX */
		};
	};

	usart3_pins_d: usart3-3 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 10, AF7)>, /* USART3_TX */
				 <STM32_PINMUX('G', 8, AF8)>; /* USART3_RTS */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 9, AF7)>, /* USART3_RX */
				 <STM32_PINMUX('D', 11, AF7)>; /* USART3_CTS_NSS */
			bias-disable;
		};
	};

	usart3_idle_pins_d: usart3-idle-3 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 10, ANALOG)>, /* USART3_TX */
				 <STM32_PINMUX('G', 8, ANALOG)>, /* USART3_RTS */
				 <STM32_PINMUX('D', 11, ANALOG)>; /* USART3_CTS_NSS */
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 9, AF7)>; /* USART3_RX */
			bias-disable;
		};
	};

	usart3_sleep_pins_d: usart3-sleep-3 {
		pins {
			pinmux = <STM32_PINMUX('B', 10, ANALOG)>, /* USART3_TX */
				 <STM32_PINMUX('G', 8, ANALOG)>, /* USART3_RTS */
				 <STM32_PINMUX('D', 11, ANALOG)>, /* USART3_CTS_NSS */
				 <STM32_PINMUX('D', 9, ANALOG)>; /* USART3_RX */
		};
	};

	usbotg_hs_pins_a: usbotg-hs-0 {
		pins {
			pinmux = <STM32_PINMUX('A', 10, ANALOG)>; /* OTG_ID */
		};
	};

	usbotg_fs_dp_dm_pins_a: usbotg-fs-dp-dm-0 {
		pins {
			pinmux = <STM32_PINMUX('A', 11, ANALOG)>, /* OTG_FS_DM */
				 <STM32_PINMUX('A', 12, ANALOG)>; /* OTG_FS_DP */
		};
	};
};

&pinctrl_z {
	i2c2_pins_b2: i2c2-0 {
		pins {
			pinmux = <STM32_PINMUX('Z', 0, AF3)>; /* I2C2_SCL */
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	i2c2_sleep_pins_b2: i2c2-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('Z', 0, ANALOG)>; /* I2C2_SCL */
		};
	};

	i2c4_pins_a: i2c4-0 {
		pins {
			pinmux = <STM32_PINMUX('Z', 4, AF6)>, /* I2C4_SCL */
				 <STM32_PINMUX('Z', 5, AF6)>; /* I2C4_SDA */
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	i2c4_sleep_pins_a: i2c4-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('Z', 4, ANALOG)>, /* I2C4_SCL */
				 <STM32_PINMUX('Z', 5, ANALOG)>; /* I2C4_SDA */
		};
	};

	i2c6_pins_a: i2c6-0 {
		pins {
			pinmux = <STM32_PINMUX('Z', 6, AF2)>, /* I2C6_SCL */
				 <STM32_PINMUX('Z', 7, AF2)>; /* I2C6_SDA */
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	i2c6_sleep_pins_a: i2c6-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('Z', 6, ANALOG)>, /* I2C6_SCL */
				 <STM32_PINMUX('Z', 7, ANALOG)>; /* I2C6_SDA */
		};
	};

	spi1_pins_a: spi1-0 {
		pins1 {
			pinmux = <STM32_PINMUX('Z', 0, AF5)>, /* SPI1_SCK */
				 <STM32_PINMUX('Z', 2, AF5)>; /* SPI1_MOSI */
			bias-disable;
			drive-push-pull;
			slew-rate = <1>;
		};

		pins2 {
			pinmux = <STM32_PINMUX('Z', 1, AF5)>; /* SPI1_MISO */
			bias-disable;
		};
	};

	spi1_pins_b: spi1-1 {
		pins1 {
			pinmux = <STM32_PINMUX('A', 5, AF5)>, /* SPI1_SCK */
				 <STM32_PINMUX('B', 5, AF5)>; /* SPI1_MOSI */
			bias-disable;
			drive-push-pull;
			slew-rate = <1>;
		};

		pins2 {
			pinmux = <STM32_PINMUX('A', 6, AF5)>; /* SPI1_MISO */
			bias-disable;
		};
	};
};
