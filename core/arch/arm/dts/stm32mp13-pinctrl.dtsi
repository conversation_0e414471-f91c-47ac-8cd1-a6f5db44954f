// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2019-2022 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>>
 */
#include <dt-bindings/pinctrl/stm32-pinfunc.h>

&pinctrl {
	i2c4_pins_a: i2c4-0 {
		pins {
			pinmux = <STM32_PINMUX('E', 15, AF6)>, /* I2C4_SCL */
				 <STM32_PINMUX('B', 9, AF6)>; /* I2C4_SDA */
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	uart4_pins_a: uart4-0 {
		pins1 {
			pinmux = <STM32_PINMUX('D', 6, AF8)>; /* UART4_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 8, AF8)>; /* UART4_RX */
			bias-disable;
		};
	};

	usart1_pins_a: usart1-0 {
		pins1 {
			pinmux = <STM32_PINMUX('C', 0, AF7)>, /* USART1_TX */
				 <STM32_PINMUX('C', 2, AF7)>; /* USART1_RTS */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('B', 0, AF4)>, /* USART1_RX */
				 <STM32_PINMUX('A', 7, AF7)>; /* USART1_CTS_NSS */
			bias-pull-up;
		};
	};

	uart8_pins_a: uart8-0 {
		pins1 {
			pinmux = <STM32_PINMUX('E', 1, AF8)>; /* UART8_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('F', 9, AF8)>; /* UART8_RX */
			bias-pull-up;
		};
	};
};
