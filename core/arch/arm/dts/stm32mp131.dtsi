// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2021-2022 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>> for STMicroelectronics.
 */

#include <dt-bindings/clock/stm32mp13-clks.h>
#include <dt-bindings/clock/stm32mp13-clksrc.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/reset/stm32mp13-resets.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0>;
		};
	};

	intc: interrupt-controller@a0021000 {
		compatible = "arm,cortex-a7-gic";
		#interrupt-cells = <3>;
		interrupt-controller;
		reg = <0xa0021000 0x1000>,
		      <0xa0022000 0x2000>;
	};

	psci {
		compatible = "arm,psci-1.0";
		method = "smc";
	};

	clocks {
		clk_hse: clk-hse {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
		};

		clk_hsi: clk-hsi {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <64000000>;
		};

		clk_lse: clk-lse {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32768>;
		};

		clk_lsi: clk-lsi {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32000>;
		};

		clk_csi: clk-csi {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <4000000>;
		};

		clk_i2sin: clk-i2sin {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <19000000>;
		};

	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		interrupt-parent = <&intc>;
		ranges;

		usart3: serial@4000f000 {
			compatible = "st,stm32h7-uart";
			reg = <0x4000f000 0x400>;
			interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		uart4: serial@40010000 {
			compatible = "st,stm32h7-uart";
			reg = <0x40010000 0x400>;
			interrupts = <GIC_SPI 53 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc UART4_K>;
			status = "disabled";
		};

		uart5: serial@40011000 {
			compatible = "st,stm32h7-uart";
			reg = <0x40011000 0x400>;
			interrupts = <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		uart7: serial@40018000 {
			compatible = "st,stm32h7-uart";
			reg = <0x40018000 0x400>;
			interrupts = <GIC_SPI 83 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		uart8: serial@40019000 {
			compatible = "st,stm32h7-uart";
			reg = <0x40019000 0x400>;
			interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		usart6: serial@44003000 {
			compatible = "st,stm32h7-uart";
			reg = <0x44003000 0x400>;
			interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		usart1: serial@4c000000 {
			compatible = "st,stm32h7-uart";
			reg = <0x4c000000 0x400>;
			interrupts = <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		usart2: serial@4c001000 {
			compatible = "st,stm32h7-uart";
			reg = <0x4c001000 0x400>;
			interrupts = <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		i2c3: i2c@4c004000 {
			compatible = "st,stm32mp13-i2c";
			reg = <0x4c004000 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;
			st,syscfg-fmp = <&syscfg 0x4 0x4>;
			i2c-analog-filter;
			status = "disabled";
		};

		i2c4: i2c@4c005000 {
			compatible = "st,stm32mp13-i2c";
			reg = <0x4c005000 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;
			st,syscfg-fmp = <&syscfg 0x4 0x8>;
			i2c-analog-filter;
			status = "disabled";
		};

		i2c5: i2c@4c006000 {
			compatible = "st,stm32mp13-i2c";
			reg = <0x4c006000 0x400>;
			#address-cells = <1>;
			#size-cells = <0>;
			st,syscfg-fmp = <&syscfg 0x4 0x10>;
			i2c-analog-filter;
			status = "disabled";
		};

		rcc: rcc@50000000 {
			compatible = "st,stm32mp13-rcc", "syscon";
			reg = <0x50000000 0x1000>;
			#address-cells = <1>;
			#size-cells = <0>;
			#clock-cells = <1>;
			#reset-cells = <1>;
			clocks = <&clk_hse>, <&clk_hsi>, <&clk_lse>, <&clk_lsi>, <&clk_csi>, <&clk_i2sin>;
			clock-names = "clk-hse", "clk-hsi", "clk-lse", "clk-lsi", "clk-csi", "clk-i2sin";
			interrupts = <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>;
			secure-interrupts = <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>;
			secure-interrupt-names = "wakeup";
		};

		syscfg: syscon@50020000 {
			compatible = "st,stm32mp157-syscfg", "syscon";
			reg = <0x50020000 0x400>;
		};

		bsec: efuse@5c005000 {
			compatible = "st,stm32mp15-bsec";
			reg = <0x5c005000 0x400>;
			#address-cells = <1>;
			#size-cells = <1>;
		};

		tzc400: tzc@5c006000 {
			compatible = "st,stm32mp1-tzc";
			reg = <0x5c006000 0x1000>;
			interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>;
			st,mem-map = <0xc0000000 0x40000000>;
			clocks = <&rcc TZC>;
		};

		etzpc: etzpc@5c007000 {
			compatible = "st,stm32-etzpc";
			reg = <0x5C007000 0x400>;
		};

		stgen: stgen@5c008000 {
			compatible = "st,stm32-stgen";
			reg = <0x5C008000 0x1000>;
		};

		pinctrl: pin-controller@******** {
			#address-cells = <1>;
			#size-cells = <1>;
			compatible = "st,stm32mp135-pinctrl";
			ranges = <0 0x******** 0x8400>;
			pins-are-numbered;

			gpioa: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				clocks = <&rcc GPIOA>;
				reg = <0x0 0x400>;
				st,bank-name = "GPIOA";
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 0 16>;
			};

			gpiob: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				clocks = <&rcc GPIOB>;
				reg = <0x1000 0x400>;
				st,bank-name = "GPIOB";
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 16 16>;
			};

			gpioc: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				clocks = <&rcc GPIOC>;
				reg = <0x2000 0x400>;
				st,bank-name = "GPIOC";
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 32 16>;
			};

			gpiod: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				clocks = <&rcc GPIOD>;
				reg = <0x3000 0x400>;
				st,bank-name = "GPIOD";
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 48 16>;
			};

			gpioe: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				clocks = <&rcc GPIOE>;
				reg = <0x4000 0x400>;
				st,bank-name = "GPIOE";
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 64 16>;
			};

			gpiof: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				clocks = <&rcc GPIOF>;
				reg = <0x5000 0x400>;
				st,bank-name = "GPIOF";
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 80 16>;
			};

			gpiog: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				clocks = <&rcc GPIOG>;
				reg = <0x6000 0x400>;
				st,bank-name = "GPIOG";
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 96 16>;
			};

			gpioh: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				clocks = <&rcc GPIOH>;
				reg = <0x7000 0x400>;
				st,bank-name = "GPIOH";
				ngpios = <15>;
				gpio-ranges = <&pinctrl 0 112 15>;
			};

			gpioi: gpio@5000a000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				clocks = <&rcc GPIOI>;
				reg = <0x8000 0x400>;
				st,bank-name = "GPIOI";
				ngpios = <8>;
				gpio-ranges = <&pinctrl 0 128 8>;
			};
		};
	};
};
