// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * at91-sama5d27_som1_ek.dts - Device Tree file for SAMA5D27-SOM1-EK board
 *
 *  Copyright (c) 2017, Microchip Technology Inc.
 *                2016 <PERSON> <<EMAIL>>
 *                2017 <PERSON><PERSON><PERSON> <<EMAIL>>
 *                2017 <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 */
/dts-v1/;
#include "at91-sama5d27_som1.dtsi"
#include <dt-bindings/mfd/atmel-flexcom.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Atmel SAMA5D27 SOM1 EK";
	compatible = "atmel,sama5d27-som1-ek", "atmel,sama5d27-som1", "atmel,sama5d27", "atmel,sama5d2", "atmel,sama5";

	aliases {
		serial0 = &uart1;	/* DBGU */
		serial1 = &uart4;	/* mikro BUS 1 */
		serial2 = &uart2;	/* mikro BUS 2 */
		i2c1	= &i2c1;
		i2c2	= &i2c3;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	ahb {
		usb0: gadget@300000 {
			atmel,vbus-gpio = <&pioA PIN_PD20 GPIO_ACTIVE_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usba_vbus>;
			status = "okay";
		};

		usb1: ohci@400000 {
			num-ports = <3>;
			atmel,vbus-gpio = <0 /* &pioA PIN_PD20 GPIO_ACTIVE_HIGH */
					   &pioA PIN_PA27 GPIO_ACTIVE_HIGH
					   0
					  >;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb_default>;
			status = "okay";
		};

		usb2: ehci@500000 {
			status = "okay";
		};

		sdmmc0: sdio-host@a0000000 {
			bus-width = <8>;
			mmc-ddr-3_3v;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_sdmmc0_default>;
			status = "okay";
		};

		sdmmc1: sdio-host@b0000000 {
			bus-width = <4>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_sdmmc1_default>;
			status = "okay";
		};

		apb {
			isc: isc@f0008000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_isc_base &pinctrl_isc_data_8bit &pinctrl_isc_data_9_10 &pinctrl_isc_data_11_12>;
				status = "okay";
			};

			qspi1: spi@f0024000 {
				status = "okay";
			};

			spi0: spi@f8000000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_spi0_default>;
				status = "okay";
			};

			macb0: ethernet@f8008000 {
				status = "okay";
			};

			tcb0: timer@f800c000 {
				timer0: timer@0 {
					compatible = "atmel,tcb-timer";
					reg = <0>;
				};

				timer1: timer@1 {
					compatible = "atmel,tcb-timer";
					reg = <1>;
				};
			};

			uart1: serial@f8020000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_uart1_default>;
				atmel,use-dma-rx;
				atmel,use-dma-tx;
				status = "okay";
			};

			uart2: serial@f8024000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_mikrobus2_uart>;
				atmel,use-dma-rx;
				atmel,use-dma-tx;
				status = "okay";
			};

			pwm0: pwm@f802c000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_mikrobus1_pwm &pinctrl_mikrobus2_pwm>;
				status = "disabled"; /* Conflict with leds. */
			};

			flx1: flexcom@f8038000 {
				atmel,flexcom-mode = <ATMEL_FLEXCOM_MODE_TWI>;
				status = "okay";

				i2c3: i2c@600 {
					dmas = <0>, <0>;
					i2c-analog-filter;
					i2c-digital-filter;
					i2c-digital-filter-width-ns = <35>;
					pinctrl-names = "default";
					pinctrl-0 = <&pinctrl_mikrobus_i2c>;
					status = "okay";
				};
			};

			shdwc@f8048010 {
				debounce-delay-us = <976>;
				atmel,wakeup-rtc-timer;

				input@0 {
					reg = <0>;
				};
			};

			uart3: serial@fc008000 {
				atmel,use-dma-rx;
				atmel,use-dma-tx;
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_uart3_default>;
				status = "disabled"; /* Conflict with isc. */
			};

			uart4: serial@fc00c000 {
				atmel,use-dma-rx;
				atmel,use-dma-tx;
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_mikrobus1_uart>;
				status = "okay";
			};

			flx3: flexcom@fc014000 {
				atmel,flexcom-mode = <ATMEL_FLEXCOM_MODE_SPI>;
				status = "disabled";

				uart8: serial@200 {
					dmas = <0>, <0>;
					pinctrl-names = "default";
					pinctrl-0 = <&pinctrl_flx3_default>;
					status = "disabled"; /* Conflict with isc. */
				};

				spi5: spi@400 {
					dmas = <0>, <0>;
					pinctrl-names = "default";
					pinctrl-0 = <&pinctrl_flx3_default>;
					status = "disabled"; /* Conflict with isc. */
				};
			};

			flx4: flexcom@fc018000 {
				atmel,flexcom-mode = <ATMEL_FLEXCOM_MODE_SPI>;
				status = "okay";

				uart9: serial@200 {
					dmas = <0>, <0>;
					pinctrl-names = "default";
					pinctrl-0 = <&pinctrl_flx4_default>;
					status = "disabled"; /* Conflict with spi6 and i2c6. */
				};

				spi6: spi@400 {
					dmas = <0>, <0>;
					pinctrl-names = "default";
					pinctrl-0 = <&pinctrl_mikrobus_spi &pinctrl_mikrobus1_spi_cs &pinctrl_mikrobus2_spi_cs>;
					status = "okay"; /* Conflict with uart5 and i2c6. */
				};

				i2c6: i2c@600 {
					dmas = <0>, <0>;
					pinctrl-names = "default";
					pinctrl-0 = <&pinctrl_flx4_default>;
					status = "disabled"; /* Conflict with uart5 and spi6. */
				};
			};

			i2c1: i2c@fc028000 {
				dmas = <0>, <0>;
				i2c-analog-filter;
				i2c-digital-filter;
				i2c-digital-filter-width-ns = <35>;
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_i2c1_default>;
				status = "okay";
			};

			adc: adc@fc030000 {
				vddana-supply = <&vddana>;
				vref-supply = <&advref>;

				status = "disabled";
			};

			pinctrl@fc038000 {

				pinctrl_can1_default: can1_default {
					pinmux = <PIN_PC26__CANTX1>,
						 <PIN_PC27__CANRX1>;
					bias-disable;
				};

				pinctrl_flx3_default: flx3_default {
					pinmux = <PIN_PC20__FLEXCOM3_IO0>,
						 <PIN_PC19__FLEXCOM3_IO1>,
						 <PIN_PC18__FLEXCOM3_IO2>,
						 <PIN_PC21__FLEXCOM3_IO3>,
						 <PIN_PC22__FLEXCOM3_IO4>;
					bias-disable;
				};

				pinctrl_i2c1_default: i2c1_default {
					pinmux = <PIN_PD4__TWD1>,
						 <PIN_PD5__TWCK1>;
					bias-disable;
				};

				pinctrl_isc_base: isc_base {
					pinmux = <PIN_PC21__ISC_PCK>,
						 <PIN_PC22__ISC_VSYNC>,
						 <PIN_PC23__ISC_HSYNC>,
						 <PIN_PC24__ISC_MCK>;
					bias-disable;
				};

				pinctrl_isc_data_8bit: isc_data_8bit {
					pinmux = <PIN_PC20__ISC_D11>,
						 <PIN_PC19__ISC_D10>,
						 <PIN_PC18__ISC_D9>,
						 <PIN_PC17__ISC_D8>,
						 <PIN_PC16__ISC_D7>,
						 <PIN_PC15__ISC_D6>,
						 <PIN_PC14__ISC_D5>,
						 <PIN_PC13__ISC_D4>;
					bias-disable;
				};

				pinctrl_isc_data_9_10: isc_data_9_10 {
					pinmux = <PIN_PC12__ISC_D3>,
						 <PIN_PC11__ISC_D2>;
					bias-disable;
				};

				pinctrl_isc_data_11_12: isc_data_11_12 {
					pinmux = <PIN_PC10__ISC_D1>,
						 <PIN_PC9__ISC_D0>;
					bias-disable;
				};

				pinctrl_key_gpio_default: key_gpio_default {
					pinmux = <PIN_PA29__GPIO>;
					bias-pull-up;
				};

				pinctrl_led_gpio_default: led_gpio_default {
					pinmux = <PIN_PA10__GPIO>,
						 <PIN_PB1__GPIO>,
						 <PIN_PA31__GPIO>;
					bias-pull-up;
				};

				pinctrl_sdmmc0_default: sdmmc0_default {
					cmd_data {
						pinmux = <PIN_PA1__SDMMC0_CMD>,
							 <PIN_PA2__SDMMC0_DAT0>,
							 <PIN_PA3__SDMMC0_DAT1>,
							 <PIN_PA4__SDMMC0_DAT2>,
							 <PIN_PA5__SDMMC0_DAT3>,
							 <PIN_PA6__SDMMC0_DAT4>,
							 <PIN_PA7__SDMMC0_DAT5>,
							 <PIN_PA8__SDMMC0_DAT6>,
							 <PIN_PA9__SDMMC0_DAT7>;
						bias-disable;
					};

					ck_cd_vddsel {
						pinmux = <PIN_PA0__SDMMC0_CK>,
							 <PIN_PA11__SDMMC0_VDDSEL>,
							 <PIN_PA13__SDMMC0_CD>;
						bias-disable;
					};
				};

				pinctrl_sdmmc1_default: sdmmc1_default {
					cmd_data {
						pinmux = <PIN_PA28__SDMMC1_CMD>,
							 <PIN_PA18__SDMMC1_DAT0>,
							 <PIN_PA19__SDMMC1_DAT1>,
							 <PIN_PA20__SDMMC1_DAT2>,
							 <PIN_PA21__SDMMC1_DAT3>;
						bias-disable;
					};

					conf-ck_cd {
						pinmux = <PIN_PA22__SDMMC1_CK>,
							 <PIN_PA30__SDMMC1_CD>;
						bias-disable;
					};
				};

				pinctrl_spi0_default: spi0_default {
					pinmux = <PIN_PA14__SPI0_SPCK>,
						 <PIN_PA15__SPI0_MOSI>,
						 <PIN_PA16__SPI0_MISO>,
						 <PIN_PA17__SPI0_NPCS0>;
					bias-disable;
				};

				pinctrl_uart1_default: uart1_default {
					pinmux = <PIN_PD2__URXD1>,
						 <PIN_PD3__UTXD1>;
					bias-disable;
				};

				pinctrl_uart3_default: uart3_default {
					pinmux = <PIN_PC12__URXD3>,
						 <PIN_PC13__UTXD3>;
					bias-disable;
				};

				pinctrl_usb_default: usb_default {
					pinmux = <PIN_PA27__GPIO>,
						 <PIN_PD19__GPIO>;
					bias-disable;
				};

				pinctrl_usba_vbus: usba_vbus {
					pinmux = <PIN_PD20__GPIO>;
					bias-disable;
				};

				pinctrl_mikrobus1_an: mikrobus1_an {
					pinmux = <PIN_PD25__GPIO>;
					bias-disable;
				};

				pinctrl_mikrobus2_an: mikrobus2_an {
					pinmux = <PIN_PD26__GPIO>;
					bias-disable;
				};

				pinctrl_mikrobus1_rst: mikrobus1_rst {
					pinmux = <PIN_PB2__GPIO>;
					bias-disable;
				};

				pinctrl_mikrobus2_rst: mikrobus2_rst {
					pinmux = <PIN_PA26__GPIO>;
					bias-disable;
				};

				pinctrl_mikrobus1_spi_cs: mikrobus1_spi_cs {
					pinmux = <PIN_PD0__FLEXCOM4_IO4>;
					bias-disable;
				};

				pinctrl_mikrobus2_spi_cs: mikrobus2_spi_cs {
					pinmux = <PIN_PC31__FLEXCOM4_IO3>;
					bias-disable;
				};

				pinctrl_mikrobus_spi: mikrobus_spi {
					pinmux = <PIN_PC28__FLEXCOM4_IO0>,
						 <PIN_PC29__FLEXCOM4_IO1>,
						 <PIN_PC30__FLEXCOM4_IO2>;
					bias-disable;
				};

				pinctrl_mikrobus1_pwm: mikrobus1_pwm {
					pinmux = <PIN_PB1__PWML1>;
					bias-disable;
				};

				pinctrl_mikrobus2_pwm: mikrobus2_pwm {
					pinmux = <PIN_PA31__PWML0>;
					bias-disable;
				};

				pinctrl_mikrobus1_int: mikrobus1_int {
					pinmux = <PIN_PB0__GPIO>;
					bias-disable;
				};

				pinctrl_mikrobus2_int: mikrobus2_int {
					pinmux = <PIN_PA25__GPIO>;
					bias-disable;
				};

				pinctrl_mikrobus1_uart: mikrobus1_uart {
					pinmux = <PIN_PB3__URXD4>,
						 <PIN_PB4__UTXD4>;
					bias-disable;
				};

				pinctrl_mikrobus2_uart: mikrobus2_uart {
					pinmux = <PIN_PD23__URXD2>,
						 <PIN_PD24__UTXD2>;
					bias-disable;
				};

				pinctrl_mikrobus_i2c: mikrobus1_i2c {
					pinmux = <PIN_PA24__FLEXCOM1_IO0>,
						 <PIN_PA23__FLEXCOM1_IO1>;
					bias-disable;
				};

				pinctrl_flx4_default: flx4_uart_default {
					pinmux = <PIN_PC28__FLEXCOM4_IO0>,
						 <PIN_PC29__FLEXCOM4_IO1>,
						 <PIN_PC30__FLEXCOM4_IO2>,
						 <PIN_PC31__FLEXCOM4_IO3>,
						 <PIN_PD0__FLEXCOM4_IO4>;
					bias-disable;
				};
			};

			can1: can@fc050000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_can1_default>;
				status = "okay";
			};
		};
	};

	gpio_keys {
		compatible = "gpio-keys";

		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_key_gpio_default>;

		pb4 {
			label = "USER";
			gpios = <&pioA PIN_PA29 GPIO_ACTIVE_LOW>;
			/* linux,code = <KEY_PROG1>; BSD license issue */
			wakeup-source;
		};
	};

	leds {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_led_gpio_default>;
		status = "okay"; /* Conflict with pwm0. */

		red {
			label = "red";
			gpios = <&pioA PIN_PA10 GPIO_ACTIVE_HIGH>;
		};

		green {
			label = "green";
			gpios = <&pioA PIN_PB1 GPIO_ACTIVE_HIGH>;
		};

		blue {
			label = "blue";
			gpios = <&pioA PIN_PA31 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
		};
	};

	vddin_3v3: fixed-regulator-vddin_3v3 {
		compatible = "regulator-fixed";

		regulator-name = "VDDIN_3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		regulator-boot-on;
		status = "okay";
	};

	vddana: fixed-regulator-vddana {
		compatible = "regulator-fixed";

		regulator-name = "VDDANA";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&vddin_3v3>;
		status = "okay";
	};

	advref: fixed-regulator-advref {
		compatible = "regulator-fixed";

		regulator-name = "advref";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&vddana>;
		status = "okay";
	};
};
