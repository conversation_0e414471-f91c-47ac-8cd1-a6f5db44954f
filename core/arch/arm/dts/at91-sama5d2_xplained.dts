// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * at91-sama5d2_xplained.dts - Device Tree file for SAMA5D2 Xplained board
 *
 *  Copyright (C) 2015 Atmel,
 *                2015 <PERSON> <<EMAIL>>
 */
/dts-v1/;
#include "sama5d2.dtsi"
#include "sama5d2-pinfunc.h"
#include <dt-bindings/mfd/atmel-flexcom.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/regulator/active-semi,8945a-regulator.h>

/ {
	model = "Atmel SAMA5D2 Xplained";
	compatible = "atmel,sama5d2-xplained", "atmel,sama5d2", "atmel,sama5";

	aliases {
		serial0 = &uart1;	/* DBGU */
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		i2c2 = &i2c2;		/* XPRO EXT2 */
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	clocks {
		slow_xtal {
			clock-frequency = <32768>;
		};

		main_xtal {
			clock-frequency = <12000000>;
		};
	};

	ahb {
		usb0: gadget@300000 {
			atmel,vbus-gpio = <&pioA PIN_PA31 GPIO_ACTIVE_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usba_vbus>;
			status = "okay";
		};

		usb1: ohci@400000 {
			num-ports = <3>;
			atmel,vbus-gpio = <0 /* &pioA PIN_PB9 GPIO_ACTIVE_HIGH */
					   &pioA PIN_PB10 GPIO_ACTIVE_HIGH
					   0
					  >;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb_default>;
			status = "okay";
		};

		usb2: ehci@500000 {
			status = "okay";
		};

		sdmmc0: sdio-host@a0000000 {
			bus-width = <8>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_sdmmc0_default>;
			non-removable;
			mmc-ddr-1_8v;
			status = "okay";
		};

		sdmmc1: sdio-host@b0000000 {
			bus-width = <4>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_sdmmc1_default>;
			status = "okay"; /* conflict with qspi0 */
			vqmmc-supply = <&vdd_3v3_reg>;
			vmmc-supply = <&vdd_3v3_reg>;
		};

		apb {
			qspi0: spi@f0020000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_qspi0_default>;
				status = "disabled"; /* conflict with sdmmc1 */

				flash@0 {
					#address-cells = <1>;
					#size-cells = <1>;
					compatible = "jedec,spi-nor";
					reg = <0>;
					spi-max-frequency = <80000000>;
					spi-tx-bus-width = <4>;
					spi-rx-bus-width = <4>;
					m25p,fast-read;

					at91bootstrap@0 {
						label = "at91bootstrap";
						reg = <0x00000000 0x00040000>;
					};

					bootloader@40000 {
						label = "bootloader";
						reg = <0x00040000 0x000c0000>;
					};

					bootloaderenvred@100000 {
						label = "bootloader env redundant";
						reg = <0x00100000 0x00040000>;
					};

					bootloaderenv@140000 {
						label = "bootloader env";
						reg = <0x00140000 0x00040000>;
					};

					dtb@180000 {
						label = "device tree";
						reg = <0x00180000 0x00080000>;
					};

					kernel@200000 {
						label = "kernel";
						reg = <0x00200000 0x00600000>;
					};

					misc@800000 {
						label = "misc";
						reg = <0x00800000 0x00000000>;
					};
				};
			};

			spi0: spi@f8000000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_spi0_default>;
				status = "okay";

				m25p80@0 {
					compatible = "atmel,at25df321a";
					reg = <0>;
					spi-max-frequency = <50000000>;
				};
			};

			macb0: ethernet@f8008000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_macb0_default &pinctrl_macb0_phy_irq>;
				phy-mode = "rmii";
				status = "okay";

				ethernet-phy@1 {
					reg = <0x1>;
					interrupt-parent = <&pioA>;
					interrupts = <PIN_PC9 IRQ_TYPE_LEVEL_LOW>;
				};
			};

			tcb0: timer@f800c000 {
				timer0: timer@0 {
					compatible = "atmel,tcb-timer";
					reg = <0>;
				};

				timer1: timer@1 {
					compatible = "atmel,tcb-timer";
					reg = <1>;
				};
			};

			uart1: serial@f8020000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_uart1_default>;
				atmel,use-dma-rx;
				atmel,use-dma-tx;
				status = "okay";
			};

			i2c0: i2c@f8028000 {
				dmas = <0>, <0>;
				pinctrl-names = "default", "gpio";
				pinctrl-0 = <&pinctrl_i2c0_default>;
				pinctrl-1 = <&pinctrl_i2c0_gpio>;
				sda-gpios = <&pioA PIN_PD21 GPIO_ACTIVE_HIGH>;
				scl-gpios = <&pioA PIN_PD22 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
				i2c-sda-hold-time-ns = <350>;
				status = "okay";

				pmic@5b {
					compatible = "active-semi,act8945a";
					reg = <0x5b>;
					active-semi,vsel-high;
					status = "okay";

					regulators {
						vdd_1v35_reg: REG_DCDC1 {
							regulator-name = "VDD_1V35";
							regulator-min-microvolt = <1350000>;
							regulator-max-microvolt = <1350000>;
							regulator-allowed-modes = <ACT8945A_REGULATOR_MODE_FIXED>,
										  <ACT8945A_REGULATOR_MODE_LOWPOWER>;
							regulator-initial-mode = <ACT8945A_REGULATOR_MODE_FIXED>;
							regulator-always-on;

							regulator-state-mem {
								regulator-on-in-suspend;
								regulator-suspend-min-microvolt=<1400000>;
								regulator-suspend-max-microvolt=<1400000>;
								regulator-changeable-in-suspend;
								regulator-mode=<ACT8945A_REGULATOR_MODE_LOWPOWER>;
							};
						};

						vdd_1v2_reg: REG_DCDC2 {
							regulator-name = "VDD_1V2";
							regulator-min-microvolt = <1100000>;
							regulator-max-microvolt = <1300000>;
							regulator-allowed-modes = <ACT8945A_REGULATOR_MODE_FIXED>,
										  <ACT8945A_REGULATOR_MODE_LOWPOWER>;
							regulator-initial-mode = <ACT8945A_REGULATOR_MODE_FIXED>;
							regulator-always-on;

							regulator-state-mem {
								regulator-off-in-suspend;
							};
						};

						vdd_3v3_reg: REG_DCDC3 {
							regulator-name = "VDD_3V3";
							regulator-min-microvolt = <3300000>;
							regulator-max-microvolt = <3300000>;
							regulator-allowed-modes = <ACT8945A_REGULATOR_MODE_FIXED>,
										  <ACT8945A_REGULATOR_MODE_LOWPOWER>;
							regulator-initial-mode = <ACT8945A_REGULATOR_MODE_FIXED>;
							regulator-always-on;

							regulator-state-mem {
								regulator-off-in-suspend;
							};
						};

						vdd_fuse_reg: REG_LDO1 {
							regulator-name = "VDD_FUSE";
							regulator-min-microvolt = <2500000>;
							regulator-max-microvolt = <2500000>;
							regulator-allowed-modes = <ACT8945A_REGULATOR_MODE_NORMAL>,
										  <ACT8945A_REGULATOR_MODE_LOWPOWER>;
							regulator-initial-mode = <ACT8945A_REGULATOR_MODE_NORMAL>;
							regulator-always-on;

							regulator-state-mem {
								regulator-off-in-suspend;
							};
						};

						vdd_3v3_lp_reg: REG_LDO2 {
							regulator-name = "VDD_3V3_LP";
							regulator-min-microvolt = <3300000>;
							regulator-max-microvolt = <3300000>;
							regulator-allowed-modes = <ACT8945A_REGULATOR_MODE_NORMAL>,
										  <ACT8945A_REGULATOR_MODE_LOWPOWER>;
							regulator-initial-mode = <ACT8945A_REGULATOR_MODE_NORMAL>;
							regulator-always-on;

							regulator-state-mem {
								regulator-off-in-suspend;
							};
						};

						vdd_led_reg: REG_LDO3 {
							regulator-name = "VDD_LED";
							regulator-min-microvolt = <3300000>;
							regulator-max-microvolt = <3300000>;
							regulator-allowed-modes = <ACT8945A_REGULATOR_MODE_NORMAL>,
										  <ACT8945A_REGULATOR_MODE_LOWPOWER>;
							regulator-initial-mode = <ACT8945A_REGULATOR_MODE_NORMAL>;
							regulator-always-on;

							regulator-state-mem {
								regulator-off-in-suspend;
							};
						};

						vdd_sdhc_1v8_reg: REG_LDO4 {
							regulator-name = "VDD_SDHC_1V8";
							regulator-min-microvolt = <1800000>;
							regulator-max-microvolt = <1800000>;
							regulator-allowed-modes = <ACT8945A_REGULATOR_MODE_NORMAL>,
										  <ACT8945A_REGULATOR_MODE_LOWPOWER>;
							regulator-initial-mode = <ACT8945A_REGULATOR_MODE_NORMAL>;
							regulator-always-on;

							regulator-state-mem {
								regulator-off-in-suspend;
							};
						};
					};

					charger {
						compatible = "active-semi,act8945a-charger";
						pinctrl-names = "default";
						pinctrl-0 = <&pinctrl_charger_chglev &pinctrl_charger_lbo &pinctrl_charger_irq>;
						interrupt-parent = <&pioA>;
						interrupts = <PIN_PB13 IRQ_TYPE_EDGE_RISING>;

						active-semi,chglev-gpios = <&pioA PIN_PA12 GPIO_ACTIVE_HIGH>;
						active-semi,lbo-gpios = <&pioA PIN_PC8 GPIO_ACTIVE_LOW>;
						active-semi,input-voltage-threshold-microvolt = <6600>;
						active-semi,precondition-timeout = <40>;
						active-semi,total-timeout = <3>;
						status = "okay";
					};
				};
			};

			pwm0: pwm@f802c000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_pwm0_pwm2_default>;
				status = "disabled"; /* conflict with leds */
			};

			flx0: flexcom@f8034000 {
				atmel,flexcom-mode = <ATMEL_FLEXCOM_MODE_USART>;
				status = "disabled"; /* conflict with ISC_D2 & ISC_D3 data pins */

				uart5: serial@200 {
					dmas = <0>, <0>;
					pinctrl-names = "default";
					pinctrl-0 = <&pinctrl_flx0_default>;
					status = "okay";
				};

				i2c2: i2c@600 {
					dmas = <0>, <0>;
					pinctrl-names = "default", "gpio";
					pinctrl-0 = <&pinctrl_flx0_default>;
					pinctrl-1 = <&pinctrl_i2c2_gpio>;
					sda-gpios = <&pioA PIN_PB28 GPIO_ACTIVE_HIGH>;
					scl-gpios = <&pioA PIN_PB29 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
					i2c-sda-hold-time-ns = <350>;
					i2c-analog-filter;
					i2c-digital-filter;
					i2c-digital-filter-width-ns = <35>;
					status = "disabled"; /* conflict with ISC_D2 & ISC_D3 data pins */
				};
			};

			shdwc@f8048010 {
				debounce-delay-us = <976>;
				atmel,wakeup-rtc-timer;

				input@0 {
					reg = <0>;
				};
			};

			i2s0: i2s@f8050000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_i2s0_default>;
				status = "disabled"; /* conflict with can0 */
			};

			can0: can@f8054000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_can0_default>;
				status = "okay";
			};

			uart3: serial@fc008000 {
				atmel,use-dma-rx;
				atmel,use-dma-tx;
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_uart3_default>;
				status = "okay";
			};

			flx4: flexcom@fc018000 {
				atmel,flexcom-mode = <ATMEL_FLEXCOM_MODE_TWI>;
				status = "okay";

				i2c6: i2c@600 {
					dmas = <0>, <0>;
					pinctrl-names = "default", "gpio";
					pinctrl-0 = <&pinctrl_flx4_default>;
					pinctrl-1 = <&pinctrl_flx4_gpio>;
					sda-gpios = <&pioA PIN_PD12 GPIO_ACTIVE_HIGH>;
					scl-gpios = <&pioA PIN_PD13 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
					i2c-analog-filter;
					i2c-digital-filter;
					i2c-digital-filter-width-ns = <35>;
					status = "okay";
				};
			};

			i2c1: i2c@fc028000 {
				dmas = <0>, <0>;
				pinctrl-names = "default", "gpio";
				pinctrl-0 = <&pinctrl_i2c1_default>;
				i2c-analog-filter;
				i2c-digital-filter;
				i2c-digital-filter-width-ns = <35>;
				pinctrl-1 = <&pinctrl_i2c1_gpio>;
				sda-gpios = <&pioA PIN_PD4 GPIO_ACTIVE_HIGH>;
				scl-gpios = <&pioA PIN_PD5 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
				status = "okay";

				at24@54 {
					compatible = "atmel,24c02";
					reg = <0x54>;
					pagesize = <16>;
				};
			};

			adc: adc@fc030000 {
				vddana-supply = <&vdd_3v3_lp_reg>;
				vref-supply = <&vdd_3v3_lp_reg>;
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_adc_default &pinctrl_adtrg_default>;
				status = "okay";
			};

			pinctrl@fc038000 {
				/*
				 * There is no real pinmux for ADC, if the pin
				 * is not requested by another peripheral then
				 * the muxing is done when channel is enabled.
				 * Requesting pins for ADC is GPIO is
				 * encouraged to prevent conflicts and to
				 * disable bias in order to be in the same
				 * state when the pin is not muxed to the adc.
				 */
				pinctrl_adc_default: adc_default {
					pinmux = <PIN_PD23__GPIO>;
					bias-disable;
				};

				pinctrl_can0_default: can0_default {
					pinmux = <PIN_PC10__CANTX0>,
						 <PIN_PC11__CANRX0>;
					bias-disable;
				};

				pinctrl_can1_default: can1_default {
					pinmux = <PIN_PC26__CANTX1>,
						 <PIN_PC27__CANRX1>;
					bias-disable;
				};

				/*
				 * The ADTRG pin can work on any edge type.
				 * In here it's being pulled up, so need to
				 * connect it to ground to get an edge e.g.
				 * Trigger can be configured on falling, rise
				 * or any edge, and the pull-up can be changed
				 * to pull-down or left floating according to
				 * needs.
				 */
				pinctrl_adtrg_default: adtrg_default {
					pinmux = <PIN_PD31__ADTRG>;
					bias-pull-up;
				};

				pinctrl_charger_chglev: charger_chglev {
					pinmux = <PIN_PA12__GPIO>;
					bias-disable;
				};

				pinctrl_charger_irq: charger_irq {
					pinmux = <PIN_PB13__GPIO>;
					bias-disable;
				};

				pinctrl_charger_lbo: charger_lbo {
					pinmux = <PIN_PC8__GPIO>;
					bias-pull-up;
				};

				pinctrl_classd_default_pfets: classd_default_pfets {
					pinmux = <PIN_PB1__CLASSD_R0>,
						 <PIN_PB3__CLASSD_R2>;
					bias-pull-up;
				};

				pinctrl_classd_default_nfets: classd_default_nfets {
					pinmux = <PIN_PB2__CLASSD_R1>,
						 <PIN_PB4__CLASSD_R3>;
					bias-pull-down;
				};

				pinctrl_flx0_default: flx0_default {
					pinmux = <PIN_PB28__FLEXCOM0_IO0>,
						 <PIN_PB29__FLEXCOM0_IO1>;
					bias-disable;
				};

				pinctrl_flx4_default: flx4_default {
					pinmux = <PIN_PD12__FLEXCOM4_IO0>,
						 <PIN_PD13__FLEXCOM4_IO1>;
					bias-disable;
				};

				pinctrl_flx4_gpio: flx4_gpio {
					pinmux = <PIN_PD12__GPIO>,
						 <PIN_PD13__GPIO>;
					bias-disable;
				};

				pinctrl_i2c0_default: i2c0_default {
					pinmux = <PIN_PD21__TWD0>,
						 <PIN_PD22__TWCK0>;
					bias-disable;
				};

				pinctrl_i2c0_gpio: i2c0_gpio {
					pinmux = <PIN_PD21__GPIO>,
						 <PIN_PD22__GPIO>;
					bias-disable;
				};

				pinctrl_i2c1_default: i2c1_default {
					pinmux = <PIN_PD4__TWD1>,
						 <PIN_PD5__TWCK1>;
					bias-disable;
				};

				pinctrl_i2c1_gpio: i2c1_gpio {
					pinmux = <PIN_PD4__GPIO>,
						 <PIN_PD5__GPIO>;
					bias-disable;
				};

				pinctrl_i2c2_gpio: i2c2_gpio {
					pinmux = <PIN_PB28__GPIO>,
						 <PIN_PB29__GPIO>;
					bias-disable;
				};

				pinctrl_i2s0_default: i2s0_default {
					pinmux = <PIN_PC1__I2SC0_CK>,
						 <PIN_PC2__I2SC0_MCK>,
						 <PIN_PC3__I2SC0_WS>,
						 <PIN_PC4__I2SC0_DI0>,
						 <PIN_PC5__I2SC0_DO0>;
					bias-disable;
				};

				pinctrl_i2s1_default: i2s1_default {
					pinmux = <PIN_PA15__I2SC1_CK>,
						 <PIN_PA14__I2SC1_MCK>,
						 <PIN_PA16__I2SC1_WS>,
						 <PIN_PA17__I2SC1_DI0>,
						 <PIN_PA18__I2SC1_DO0>;
					bias-disable;
				};

				pinctrl_key_gpio_default: key_gpio_default {
					pinmux = <PIN_PB9__GPIO>;
					bias-pull-up;
				};

				pinctrl_led_gpio_default: led_gpio_default {
					pinmux = <PIN_PB0__GPIO>,
						 <PIN_PB5__GPIO>,
						 <PIN_PB6__GPIO>;
					bias-pull-up;
				};

				pinctrl_macb0_default: macb0_default {
					pinmux = <PIN_PB14__GTXCK>,
						 <PIN_PB15__GTXEN>,
						 <PIN_PB16__GRXDV>,
						 <PIN_PB17__GRXER>,
						 <PIN_PB18__GRX0>,
						 <PIN_PB19__GRX1>,
						 <PIN_PB20__GTX0>,
						 <PIN_PB21__GTX1>,
						 <PIN_PB22__GMDC>,
						 <PIN_PB23__GMDIO>;
					bias-disable;
				};

				pinctrl_macb0_phy_irq: macb0_phy_irq {
					pinmux = <PIN_PC9__GPIO>;
					bias-disable;
				};

				pinctrl_qspi0_default: qspi0_default {
					sck_cs {
						pinmux = <PIN_PA22__QSPI0_SCK>,
							 <PIN_PA23__QSPI0_CS>;
						bias-disable;
					};

					data {
						pinmux = <PIN_PA24__QSPI0_IO0>,
							 <PIN_PA25__QSPI0_IO1>,
							 <PIN_PA26__QSPI0_IO2>,
							 <PIN_PA27__QSPI0_IO3>;
						bias-pull-up;
					};
				};

				pinctrl_sdmmc0_default: sdmmc0_default {
					cmd_data {
						pinmux = <PIN_PA1__SDMMC0_CMD>,
							 <PIN_PA2__SDMMC0_DAT0>,
							 <PIN_PA3__SDMMC0_DAT1>,
							 <PIN_PA4__SDMMC0_DAT2>,
							 <PIN_PA5__SDMMC0_DAT3>,
							 <PIN_PA6__SDMMC0_DAT4>,
							 <PIN_PA7__SDMMC0_DAT5>,
							 <PIN_PA8__SDMMC0_DAT6>,
							 <PIN_PA9__SDMMC0_DAT7>;
						bias-disable;
					};

					ck_cd_rstn_vddsel {
						pinmux = <PIN_PA0__SDMMC0_CK>,
							 <PIN_PA10__SDMMC0_RSTN>,
							 <PIN_PA11__SDMMC0_VDDSEL>,
							 <PIN_PA13__SDMMC0_CD>;
						bias-disable;
					};
				};

				pinctrl_sdmmc1_default: sdmmc1_default {
					cmd_data {
						pinmux = <PIN_PA28__SDMMC1_CMD>,
							 <PIN_PA18__SDMMC1_DAT0>,
							 <PIN_PA19__SDMMC1_DAT1>,
							 <PIN_PA20__SDMMC1_DAT2>,
							 <PIN_PA21__SDMMC1_DAT3>;
						bias-disable;
					};

					conf-ck_cd {
						pinmux = <PIN_PA22__SDMMC1_CK>,
							 <PIN_PA30__SDMMC1_CD>;
						bias-disable;
					};
				};

				pinctrl_spi0_default: spi0_default {
					pinmux = <PIN_PA14__SPI0_SPCK>,
						 <PIN_PA15__SPI0_MOSI>,
						 <PIN_PA16__SPI0_MISO>,
						 <PIN_PA17__SPI0_NPCS0>;
					bias-disable;
				};

				pinctrl_uart1_default: uart1_default {
					pinmux = <PIN_PD2__URXD1>,
						 <PIN_PD3__UTXD1>;
					bias-disable;
				};

				pinctrl_uart3_default: uart3_default {
					pinmux = <PIN_PB11__URXD3>,
						 <PIN_PB12__UTXD3>;
					bias-disable;
				};

				pinctrl_usb_default: usb_default {
					pinmux = <PIN_PB10__GPIO>;
					bias-disable;
				};

				pinctrl_usba_vbus: usba_vbus {
					pinmux = <PIN_PA31__GPIO>;
					bias-disable;
				};

				pinctrl_pwm0_pwm2_default: pwm0_pwm2_default {
					pinmux = <PIN_PB5__PWMH2>,
						 <PIN_PB6__PWML2>;
					bias-pull-up;
				};
			};

			classd: classd@fc048000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_classd_default_pfets &pinctrl_classd_default_nfets>;
				atmel,pwm-type = "diff";
				atmel,non-overlap-time = <10>;
				status = "okay";
			};

			i2s1: i2s@fc04c000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_i2s1_default>;
				status = "disabled"; /* conflict with spi0, sdmmc1 */
			};

			can1: can@fc050000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_can1_default>;
				status = "okay";
			};
		};
	};

	gpio_keys {
		compatible = "gpio-keys";

		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_key_gpio_default>;

		bp1 {
			label = "PB_USER";
			gpios = <&pioA PIN_PB9 GPIO_ACTIVE_LOW>;
			/* linux,code = <KEY_PROG1>; BSD license issue */
			wakeup-source;
		};
	};

	leds {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_led_gpio_default>;
		status = "okay"; /* conflict with pwm0 */

		red {
			label = "red";
			gpios = <&pioA PIN_PB6 GPIO_ACTIVE_LOW>;
		};


		green {
			label = "green";
			gpios = <&pioA PIN_PB5 GPIO_ACTIVE_LOW>;
		};

		blue {
			label = "blue";
			gpios = <&pioA PIN_PB0 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "heartbeat";
		};
	};
};
