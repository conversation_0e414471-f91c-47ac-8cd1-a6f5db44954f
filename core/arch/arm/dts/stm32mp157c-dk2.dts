// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2019 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>> for STMicroelectronics.
 */

/dts-v1/;

#include "stm32mp157.dtsi"
#include "stm32mp15xc.dtsi"
#include "stm32mp15-pinctrl.dtsi"
#include "stm32mp15xxac-pinctrl.dtsi"
#include "stm32mp15xx-dkx.dtsi"

/ {
	model = "STMicroelectronics STM32MP157C-DK2 Discovery Board";
	compatible = "st,stm32mp157c-dk2", "st,stm32mp157";

	aliases {
		ethernet0 = &ethernet0;
		serial0 = &uart4;
		serial1 = &usart3;
		serial2 = &uart7;
		serial3 = &usart2;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};
};

&dsi {
	status = "okay";
	phy-dsi-supply = <&reg18>;

	ports {
		port@0 {
			reg = <0>;
			dsi_in: endpoint {
				remote-endpoint = <&ltdc_ep1_out>;
			};
		};

		port@1 {
			reg = <1>;
			dsi_out: endpoint {
				remote-endpoint = <&panel_in>;
			};
		};
	};

	panel@0 {
		compatible = "orisetech,otm8009a";
		reg = <0>;
		reset-gpios = <&gpioe 4 GPIO_ACTIVE_LOW>;
		power-supply = <&v3v3>;
		status = "okay";

		port {
			panel_in: endpoint {
				remote-endpoint = <&dsi_out>;
			};
		};
	};
};

&i2c1 {
	touchscreen@38 {
		compatible = "focaltech,ft6236";
		reg = <0x38>;
		interrupts = <2 2>;
		interrupt-parent = <&gpiof>;
		interrupt-controller;
		touchscreen-size-x = <480>;
		touchscreen-size-y = <800>;
		status = "okay";
	};
};

&ltdc {
	status = "okay";

	port {
		ltdc_ep1_out: endpoint@1 {
			reg = <1>;
			remote-endpoint = <&dsi_in>;
		};
	};
};

&usart2 {
	pinctrl-names = "default", "sleep", "idle";
	pinctrl-0 = <&usart2_pins_c>;
	pinctrl-1 = <&usart2_sleep_pins_c>;
	pinctrl-2 = <&usart2_idle_pins_c>;
	status = "disabled";
};

&rcc {
	status = "okay";
};

&bsec {
	board_id: board_id@ec {
		reg = <0xec 0x4>;
		st,non-secure-otp;
	};
};
