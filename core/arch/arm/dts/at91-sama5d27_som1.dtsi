// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * at91-sama5d27_som1.dtsi - Device Tree file for SAMA5D27 SoM1 board
 *
 *  Copyright (c) 2017, Microchip Technology Inc.
 *                2017 <PERSON><PERSON><PERSON> <<EMAIL>>
 *                2017 <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 */
#include "sama5d2.dtsi"
#include "sama5d2-pinfunc.h"

/ {
	model = "Atmel SAMA5D27 SoM1";
	compatible = "atmel,sama5d27-som1", "atmel,sama5d27", "atmel,sama5d2", "atmel,sama5";

	aliases {
		i2c0	= &i2c0;
	};

	clocks {
		slow_xtal {
			clock-frequency = <32768>;
		};

		main_xtal {
			clock-frequency = <24000000>;
		};
	};

	ahb {
		sdmmc0: sdio-host@a0000000 {
			microchip,sdcal-inverted;
		};

		apb {
			qspi1: spi@f0024000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_qspi1_default>;

				flash@0 {
					#address-cells = <1>;
					#size-cells = <1>;
					compatible = "jedec,spi-nor";
					reg = <0>;
					spi-max-frequency = <80000000>;
					spi-tx-bus-width = <4>;
					spi-rx-bus-width = <4>;
					m25p,fast-read;

					at91bootstrap@0 {
						label = "at91bootstrap";
						reg = <0x00000000 0x00040000>;
					};

					bootloader@40000 {
						label = "bootloader";
						reg = <0x00040000 0x000c0000>;
					};

					bootloaderenvred@100000 {
						label = "bootloader env redundant";
						reg = <0x00100000 0x00040000>;
					};

					bootloaderenv@140000 {
						label = "bootloader env";
						reg = <0x00140000 0x00040000>;
					};

					dtb@180000 {
						label = "device tree";
						reg = <0x00180000 0x00080000>;
					};

					kernel@200000 {
						label = "kernel";
						reg = <0x00200000 0x00600000>;
					};
				};
			};

			macb0: ethernet@f8008000 {
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_macb0_default>;
				phy-mode = "rmii";

				ethernet-phy@7 {
					reg = <0x7>;
					interrupt-parent = <&pioA>;
					interrupts = <PIN_PD31 IRQ_TYPE_LEVEL_LOW>;
					pinctrl-names = "default";
					pinctrl-0 = <&pinctrl_macb0_phy_irq>;
				};
			};

			i2c0: i2c@f8028000 {
				dmas = <0>, <0>;
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_i2c0_default>;
				status = "okay";

				at24@50 {
					compatible = "atmel,24c02";
					reg = <0x50>;
					pagesize = <8>;
				};
			};

			pinctrl@fc038000 {
				pinctrl_i2c0_default: i2c0_default {
					pinmux = <PIN_PD21__TWD0>,
						 <PIN_PD22__TWCK0>;
					bias-disable;
				};

				pinctrl_qspi1_default: qspi1_default {
					sck_cs {
						pinmux = <PIN_PB5__QSPI1_SCK>,
							 <PIN_PB6__QSPI1_CS>;
						bias-disable;
					};

					data {
						pinmux = <PIN_PB7__QSPI1_IO0>,
							 <PIN_PB8__QSPI1_IO1>,
							 <PIN_PB9__QSPI1_IO2>,
							 <PIN_PB10__QSPI1_IO3>;
						bias-pull-up;
					};
				};

				pinctrl_macb0_default: macb0_default {
					pinmux = <PIN_PD9__GTXCK>,
						 <PIN_PD10__GTXEN>,
						 <PIN_PD11__GRXDV>,
						 <PIN_PD12__GRXER>,
						 <PIN_PD13__GRX0>,
						 <PIN_PD14__GRX1>,
						 <PIN_PD15__GTX0>,
						 <PIN_PD16__GTX1>,
						 <PIN_PD17__GMDC>,
						 <PIN_PD18__GMDIO>;
					bias-disable;
				};

				pinctrl_macb0_phy_irq: macb0_phy_irq {
					pinmux = <PIN_PD31__GPIO>;
					bias-disable;
				};
			};
		};
	};
};
