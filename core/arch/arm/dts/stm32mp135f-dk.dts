// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2021-2022 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>> for STMicroelectronics.
 */

/dts-v1/;

#include <dt-bindings/clock/stm32mp13-clksrc.h>
#include "stm32mp135.dtsi"
#include "stm32mp13xf.dtsi"
#include "stm32mp13-pinctrl.dtsi"

/ {
	model = "STMicroelectronics STM32MP135F-DK Discovery Board";
	compatible = "st,stm32mp135f-dk", "st,stm32mp135";

	aliases {
		serial0 = &uart4;
		serial1 = &usart1;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	memory@c0000000 {
		device_type = "memory";
		reg = <0xc0000000 0x20000000>;
	};

        reserved-memory {
                #address-cells = <1>;
                #size-cells = <1>;
                ranges;

                optee_framebuffer: optee-framebuffer@dd000000 {
                        /* Secure framebuffer memory */
                        reg = <0xdd000000 0x1000000>;
                        no-map;
                };
        };

	vin: vin {
		compatible = "regulator-fixed";
		regulator-name = "vin";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
	};

	v3v3_ao: v3v3_ao {
		compatible = "regulator-fixed";
		regulator-name = "v3v3_ao";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};
};

&rcc {
	compatible = "st,stm32mp13-rcc", "syscon";

	st,clksrc = <
		CLK_MPU_PLL1P
		CLK_AXI_PLL2P
		CLK_MLAHBS_PLL3
		CLK_RTC_LSE
		CLK_MCO1_HSE
		CLK_MCO2_DISABLED
		CLK_CKPER_HSE
		CLK_ETH1_PLL4P
		CLK_ETH2_PLL4P
		CLK_SDMMC1_PLL4P
		CLK_SDMMC2_PLL4P
		CLK_STGEN_HSE
		CLK_USBPHY_HSE
		CLK_I2C4_HSI
		CLK_USBO_USBPHY
		CLK_ADC2_CKPER
		CLK_I2C12_HSI
		CLK_UART1_HSI
		CLK_UART2_HSI
		CLK_UART35_HSI
		CLK_UART4_HSI
		CLK_UART6_HSI
		CLK_UART78_HSI
		CLK_SAES_AXI
		CLK_DCMIPP_PLL2Q
		CLK_LPTIM3_PCLK3
		CLK_RNG1_PLL4R
	>;

	st,clkdiv = <
		DIV(DIV_MPU, 1)
		DIV(DIV_AXI, 0)
		DIV(DIV_MLAHB, 0)
		DIV(DIV_APB1, 1)
		DIV(DIV_APB2, 1)
		DIV(DIV_APB3, 1)
		DIV(DIV_APB4, 1)
		DIV(DIV_APB5, 2)
		DIV(DIV_APB6, 1)
		DIV(DIV_RTC, 0)
		DIV(DIV_MCO1, 0)
		DIV(DIV_MCO2, 0)
	>;

	st,pll_vco {
		pll1_vco_2000Mhz: pll1-vco-2000Mhz {
			src = < CLK_PLL12_HSE >;
			divmn = < 1 82 >;
			frac = < 0xAAA >;
		};

		pll1_vco_1300Mhz: pll1-vco-1300Mhz {
			src = < CLK_PLL12_HSE >;
			divmn = < 2 80 >;
			frac = < 0x800 >;
		};

		pll2_vco_1066Mhz: pll2-vco-1066Mhz {
			src = < CLK_PLL12_HSE >;
			divmn = < 2 65 >;
			frac = < 0x1400 >;
		};

		pll3_vco_417_8Mhz: pll3-vco-417_8Mhz {
			src = < CLK_PLL3_HSE >;
			divmn = < 1 33 >;
			frac = < 0x1a04 >;
		};

		pll4_vco_600Mhz: pll4-vco-600Mhz {
			src = < CLK_PLL4_HSE >;
			divmn = < 1 49 >;
		};
	};

	/* VCO = 1300.0 MHz => P = 650 (CPU) */
	pll1: st,pll@0 {
		compatible = "st,stm32mp1-pll";
		reg = <0>;

		st,pll = < &pll1_cfg1 >;

		pll1_cfg1: pll1_cfg1 {
			st,pll_vco = < &pll1_vco_1300Mhz >;
			st,pll_div_pqr = < 0 1 1 >;
		};

		pll1_cfg2: pll1_cfg2 {
			st,pll_vco = < &pll1_vco_2000Mhz >;
			st,pll_div_pqr = < 0 1 1 >;
		};
	};

	/* VCO = 1066.0 MHz => P = 266 (AXI), Q = 266, R = 533 (DDR) */
	pll2: st,pll@1 {
		compatible = "st,stm32mp1-pll";
		reg = <1>;

		st,pll = < &pll2_cfg1 >;

		pll2_cfg1: pll2_cfg1 {
			st,pll_vco = < &pll2_vco_1066Mhz >;
			st,pll_div_pqr = < 1 1 0 >;
		};
	};

	/* VCO = 417.8 MHz => P = 209, Q = 24, R = 11 */
	pll3: st,pll@2 {
		compatible = "st,stm32mp1-pll";
		reg = <2>;

		st,pll = < &pll3_cfg1 >;

		pll3_cfg1: pll3_cfg1 {
			st,pll_vco = < &pll3_vco_417_8Mhz >;
			st,pll_div_pqr = < 1 16 36 >;
		};
	};

	/* VCO = 600.0 MHz => P = 50, Q = 10, R = 50 */
	pll4: st,pll@3 {
		compatible = "st,stm32mp1-pll";
		reg = <3>;
		st,pll = < &pll4_cfg1 >;

		pll4_cfg1: pll4_cfg1 {
			st,pll_vco = < &pll4_vco_600Mhz >;
			st,pll_div_pqr = < 11 59 11 >;
		};
	};

	st,clk_opp {
		/* CK_MPU clock config for MP13 */
		st,ck_mpu {

			cfg_1 {
				hz = < 1000000000 >;
				st,clksrc = < CLK_MPU_PLL1P >;
				st,pll = < &pll1_cfg2 >;
			};

			cfg_2 {
				hz = < 650000000 >;
				st,clksrc = < CLK_MPU_PLL1P >;
				st,pll = < &pll1_cfg1 >;
			};
		};
	};
};

&uart4 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart4_pins_a>;
	status = "okay";
};

&usart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&usart1_pins_a>;
	uart-has-rtscts;
	status = "disabled";
};

&uart8 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart8_pins_a>;
	status = "disabled";
};
