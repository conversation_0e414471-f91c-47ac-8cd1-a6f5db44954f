// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2017 - All Rights Reserved
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>> for STMicroelectronics.
 */
/dts-v1/;

#include "stm32mp157c-ed1.dts"
#include <dt-bindings/gpio/gpio.h>
/* #include <dt-bindings/input/input.h> Remove due to BSD license issue */

/ {
	model = "STMicroelectronics STM32MP157C eval daughter on eval mother";
	compatible = "st,stm32mp157c-ev1", "st,stm32mp157c-ed1", "st,stm32mp157";

	chosen {
		stdout-path = "serial0:115200n8";
	};

	aliases {
		serial0 = &uart4;
		serial1 = &usart3;
		ethernet0 = &ethernet0;
	};

	clocks {
		clk_ext_camera: clk-ext-camera {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
		};
	};

	joystick {
		compatible = "gpio-keys";
		pinctrl-0 = <&joystick_pins>;
		pinctrl-names = "default";
		button-0 {
			label = "JoySel";
			/* linux,code = <KEY_ENTER>; BSD license issue */
			interrupt-parent = <&stmfx_pinctrl>;
			interrupts = <0 IRQ_TYPE_EDGE_RISING>;
		};
		button-1 {
			label = "JoyDown";
			/* linux,code = <KEY_DOWN>; BSD license issue */
			interrupt-parent = <&stmfx_pinctrl>;
			interrupts = <1 IRQ_TYPE_EDGE_RISING>;
		};
		button-2 {
			label = "JoyLeft";
			/* linux,code = <KEY_LEFT>; BSD license issue */
			interrupt-parent = <&stmfx_pinctrl>;
			interrupts = <2 IRQ_TYPE_EDGE_RISING>;
		};
		button-3 {
			label = "JoyRight";
			/* linux,code = <KEY_RIGHT>; BSD license issue */
			interrupt-parent = <&stmfx_pinctrl>;
			interrupts = <3 IRQ_TYPE_EDGE_RISING>;
		};
		button-4 {
			label = "JoyUp";
			/* linux,code = <KEY_UP>; BSD license issue */
			interrupt-parent = <&stmfx_pinctrl>;
			interrupts = <4 IRQ_TYPE_EDGE_RISING>;
		};
	};

	panel_backlight: panel-backlight {
		compatible = "gpio-backlight";
		gpios = <&gpiod 13 GPIO_ACTIVE_LOW>;
		default-on;
		status = "okay";
	};
};

&cec {
	pinctrl-names = "default";
	pinctrl-0 = <&cec_pins_a>;
	status = "okay";
};

&dcmi {
	status = "okay";
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&dcmi_pins_a>;
	pinctrl-1 = <&dcmi_sleep_pins_a>;

	port {
		dcmi_0: endpoint {
			remote-endpoint = <&ov5640_0>;
			bus-type = <5>;
			bus-width = <8>;
			hsync-active = <0>;
			vsync-active = <0>;
			pclk-sample = <1>;
		};
	};
};

&dsi {
	phy-dsi-supply = <&reg18>;
	status = "okay";

	ports {
		port@0 {
			reg = <0>;
			dsi_in: endpoint {
				remote-endpoint = <&ltdc_ep0_out>;
			};
		};

		port@1 {
			reg = <1>;
			dsi_out: endpoint {
				remote-endpoint = <&dsi_panel_in>;
			};
		};
	};

	panel-dsi@0 {
		compatible = "raydium,rm68200";
		reg = <0>;
		reset-gpios = <&gpiof 15 GPIO_ACTIVE_LOW>;
		backlight = <&panel_backlight>;
		power-supply = <&v3v3>;
		status = "okay";

		port {
			dsi_panel_in: endpoint {
				remote-endpoint = <&dsi_out>;
			};
		};
	};
};

&ethernet0 {
	status = "okay";
	pinctrl-0 = <&ethernet0_rgmii_pins_a>;
	pinctrl-1 = <&ethernet0_rgmii_sleep_pins_a>;
	pinctrl-names = "default", "sleep";
	phy-mode = "rgmii-id";
	max-speed = <1000>;
	phy-handle = <&phy0>;

	mdio0 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "snps,dwmac-mdio";
		phy0: ethernet-phy@0 {
			reg = <0>;
		};
	};
};

&fmc {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&fmc_pins_a>;
	pinctrl-1 = <&fmc_sleep_pins_a>;
	status = "okay";

	nand-controller@4,0 {
		status = "okay";

		nand@0 {
			reg = <0>;
			nand-on-flash-bbt;
			#address-cells = <1>;
			#size-cells = <1>;
		};
	};
};

&i2c2 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c2_pins_a>;
	pinctrl-1 = <&i2c2_sleep_pins_a>;
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	status = "okay";

	ov5640: camera@3c {
		compatible = "ovti,ov5640";
		reg = <0x3c>;
		clocks = <&clk_ext_camera>;
		clock-names = "xclk";
		DOVDD-supply = <&v2v8>;
		powerdown-gpios = <&stmfx_pinctrl 18 (GPIO_ACTIVE_HIGH | GPIO_PUSH_PULL)>;
		reset-gpios = <&stmfx_pinctrl 19 (GPIO_ACTIVE_LOW | GPIO_PUSH_PULL)>;
		rotation = <180>;
		status = "okay";

		port {
			ov5640_0: endpoint {
				remote-endpoint = <&dcmi_0>;
				bus-width = <8>;
				data-shift = <2>; /* lines 9:2 are used */
				hsync-active = <0>;
				vsync-active = <0>;
				pclk-sample = <1>;
			};
		};
	};

	stmfx: stmfx@42 {
		compatible = "st,stmfx-0300";
		reg = <0x42>;
		interrupts = <8 IRQ_TYPE_EDGE_RISING>;
		interrupt-parent = <&gpioi>;
		vdd-supply = <&v3v3>;

		stmfx_pinctrl: pinctrl {
			compatible = "st,stmfx-0300-pinctrl";
			gpio-controller;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
			gpio-ranges = <&stmfx_pinctrl 0 0 24>;

			joystick_pins: joystick-pins {
				pins = "gpio0", "gpio1", "gpio2", "gpio3", "gpio4";
				bias-pull-down;
			};
		};
	};
};

&i2c5 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c5_pins_a>;
	pinctrl-1 = <&i2c5_sleep_pins_a>;
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	status = "okay";
};

&ltdc {
	status = "okay";

	port {
		ltdc_ep0_out: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&dsi_in>;
		};
	};
};

&m_can1 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&m_can1_pins_a>;
	pinctrl-1 = <&m_can1_sleep_pins_a>;
	status = "okay";
};

&qspi {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&qspi_clk_pins_a &qspi_bk1_pins_a &qspi_bk2_pins_a>;
	pinctrl-1 = <&qspi_clk_sleep_pins_a &qspi_bk1_sleep_pins_a &qspi_bk2_sleep_pins_a>;
	reg = <0x58003000 0x1000>, <0x70000000 0x4000000>;
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	flash0: flash@0 {
		compatible = "jedec,spi-nor";
		reg = <0>;
		spi-rx-bus-width = <4>;
		spi-max-frequency = <108000000>;
		#address-cells = <1>;
		#size-cells = <1>;
	};

	flash1: flash@1 {
		compatible = "jedec,spi-nor";
		reg = <1>;
		spi-rx-bus-width = <4>;
		spi-max-frequency = <108000000>;
		#address-cells = <1>;
		#size-cells = <1>;
	};
};

&sdmmc3 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc3_b4_pins_a>;
	pinctrl-1 = <&sdmmc3_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc3_b4_sleep_pins_a>;
	broken-cd;
	st,neg-edge;
	bus-width = <4>;
	vmmc-supply = <&v3v3>;
	status = "disabled";
};

&spi1 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi1_pins_a>;
	status = "disabled";
};

&timers2 {
	/* spare dmas for other usage (un-delete to enable pwm capture) */
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
	pwm {
		pinctrl-0 = <&pwm2_pins_a>;
		pinctrl-1 = <&pwm2_sleep_pins_a>;
		pinctrl-names = "default", "sleep";
		status = "okay";
	};
	timer@1 {
		status = "okay";
	};
};

&timers8 {
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
	pwm {
		pinctrl-0 = <&pwm8_pins_a>;
		pinctrl-1 = <&pwm8_sleep_pins_a>;
		pinctrl-names = "default", "sleep";
		status = "okay";
	};
	timer@7 {
		status = "okay";
	};
};

&timers12 {
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
	pwm {
		pinctrl-0 = <&pwm12_pins_a>;
		pinctrl-1 = <&pwm12_sleep_pins_a>;
		pinctrl-names = "default", "sleep";
		status = "okay";
	};
	timer@11 {
		status = "okay";
	};
};

&usart3 {
	pinctrl-names = "default", "sleep", "idle";
	pinctrl-0 = <&usart3_pins_b>;
	pinctrl-1 = <&usart3_sleep_pins_b>;
	pinctrl-2 = <&usart3_idle_pins_b>;
	/*
	 * HW flow control USART3_RTS is optional, and isn't default wired to
	 * the connector. SB23 needs to be soldered in order to use it, and R77
	 * (ETH_CLK) should be removed.
	 */
	uart-has-rtscts;
	status = "disabled";
};

&usbh_ehci {
	phys = <&usbphyc_port0>;
	status = "okay";
};

&usbotg_hs {
	pinctrl-0 = <&usbotg_hs_pins_a>;
	pinctrl-names = "default";
	phys = <&usbphyc_port1 0>;
	phy-names = "usb2-phy";
	status = "okay";
};

&usbphyc {
	status = "okay";
};

&usbphyc_port0 {
	st,tune-hs-dc-level = <2>;
	st,enable-fs-rftime-tuning;
	st,enable-hs-rftime-reduction;
	st,trim-hs-current = <15>;
	st,trim-hs-impedance = <1>;
	st,tune-squelch-level = <3>;
	st,tune-hs-rx-offset = <2>;
	st,no-lsfs-sc;
};

&usbphyc_port1 {
	st,tune-hs-dc-level = <2>;
	st,enable-fs-rftime-tuning;
	st,enable-hs-rftime-reduction;
	st,trim-hs-current = <15>;
	st,trim-hs-impedance = <1>;
	st,tune-squelch-level = <3>;
	st,tune-hs-rx-offset = <2>;
	st,no-lsfs-sc;
};
