// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2019 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>> for STMicroelectronics.
 */

/dts-v1/;

#include "stm32mp157.dtsi"
#include "stm32mp15-pinctrl.dtsi"
#include "stm32mp15xxac-pinctrl.dtsi"
#include "stm32mp15xx-dkx.dtsi"

/ {
	model = "STMicroelectronics STM32MP157A-DK1 Discovery Board";
	compatible = "st,stm32mp157a-dk1", "st,stm32mp157";

	aliases {
		ethernet0 = &ethernet0;
		serial0 = &uart4;
		serial1 = &usart3;
		serial2 = &uart7;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};
};

&rcc {
	status = "okay";
};

&bsec {
	board_id: board_id@ec {
		reg = <0xec 0x4>;
		st,non-secure-otp;
	};
};
