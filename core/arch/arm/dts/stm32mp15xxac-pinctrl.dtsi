// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2019 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>> for STMicroelectronics.
 */

&pinctrl {
	st,package = <STM32MP_PKG_AC>;

	gpioa: gpio@50002000 {
		status = "okay";
		ngpios = <16>;
		gpio-ranges = <&pinctrl 0 0 16>;
	};

	gpiob: gpio@50003000 {
		status = "okay";
		ngpios = <16>;
		gpio-ranges = <&pinctrl 0 16 16>;
	};

	gpioc: gpio@50004000 {
		status = "okay";
		ngpios = <16>;
		gpio-ranges = <&pinctrl 0 32 16>;
	};

	gpiod: gpio@50005000 {
		status = "okay";
		ngpios = <16>;
		gpio-ranges = <&pinctrl 0 48 16>;
	};

	gpioe: gpio@50006000 {
		status = "okay";
		ngpios = <16>;
		gpio-ranges = <&pinctrl 0 64 16>;
	};

	gpiof: gpio@50007000 {
		status = "okay";
		ngpios = <16>;
		gpio-ranges = <&pinctrl 0 80 16>;
	};

	gpiog: gpio@50008000 {
		status = "okay";
		ngpios = <16>;
		gpio-ranges = <&pinctrl 0 96 16>;
	};

	gpioh: gpio@50009000 {
		status = "okay";
		ngpios = <16>;
		gpio-ranges = <&pinctrl 0 112 16>;
	};

	gpioi: gpio@5000a000 {
		status = "okay";
		ngpios = <12>;
		gpio-ranges = <&pinctrl 0 128 12>;
	};
};

&pinctrl_z {
	st,package = <STM32MP_PKG_AC>;

	gpioz: gpio@54004000 {
		status = "okay";
		ngpios = <8>;
		gpio-ranges = <&pinctrl_z 0 400 8>;
	};
};
