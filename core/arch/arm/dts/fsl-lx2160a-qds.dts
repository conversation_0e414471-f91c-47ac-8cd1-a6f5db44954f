// SPDX-License-Identifier: (GPL-2.0 OR MIT)
//
// Device Tree file for LX2160AQDS
//
// Copyright 2018-2020 NXP

/dts-v1/;

#include "fsl-lx2160a.dtsi"

/ {
	model = "NXP Layerscape LX2160AQDS";
	compatible = "fsl,lx2160a-qds", "fsl,lx2160a";

	aliases {
		crypto = &crypto;
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	sb_3v3: regulator-sb3v3 {
		compatible = "regulator-fixed";
		regulator-name = "MC34717-3.3VSB";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
		regulator-always-on;
	};

	mdio-mux-1 {
		compatible = "mdio-mux-multiplexer";
		mux-controls = <&mux 0>;
		mdio-parent-bus = <&emdio1>;
		#address-cells=<1>;
		#size-cells = <0>;

		mdio@0 { /* On-board PHY #1 RGMI1*/
			reg = <0x00>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@8 { /* On-board PHY #2 RGMI2*/
			reg = <0x8>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@18 { /* Slot #1 */
			reg = <0x18>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@19 { /* Slot #2 */
			reg = <0x19>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@1a { /* Slot #3 */
			reg = <0x1a>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@1b { /* Slot #4 */
			reg = <0x1b>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@1c { /* Slot #5 */
			reg = <0x1c>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@1d { /* Slot #6 */
			reg = <0x1d>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@1e { /* Slot #7 */
			reg = <0x1e>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@1f { /* Slot #8 */
			reg = <0x1f>;
			#address-cells = <1>;
			#size-cells = <0>;
		};
	};

	mdio-mux-2 {
		compatible = "mdio-mux-multiplexer";
		mux-controls = <&mux 1>;
		mdio-parent-bus = <&emdio2>;
		#address-cells=<1>;
		#size-cells = <0>;

		mdio@0 { /* Slot #1 (secondary EMI) */
			reg = <0x00>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@1 { /* Slot #2 (secondary EMI) */
			reg = <0x01>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@2 { /* Slot #3 (secondary EMI) */
			reg = <0x02>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@3 { /* Slot #4 (secondary EMI) */
			reg = <0x03>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@4 { /* Slot #5 (secondary EMI) */
			reg = <0x04>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@5 { /* Slot #6 (secondary EMI) */
			reg = <0x05>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@6 { /* Slot #7 (secondary EMI) */
			reg = <0x06>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mdio@7 { /* Slot #8 (secondary EMI) */
			reg = <0x07>;
			#address-cells = <1>;
			#size-cells = <0>;
		};
	};
};

&can0 {
	status = "okay";
};

&can1 {
	status = "okay";
};

&crypto {
	status = "okay";
};

&dspi0 {
	status = "okay";

	dflash0: flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <1000000>;
	};
};

&dspi1 {
	status = "okay";

	dflash1: flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <1000000>;
	};
};

&dspi2 {
	status = "okay";

	dflash2: flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <1000000>;
	};
};

&emdio1 {
	status = "okay";
};

&emdio2 {
	status = "okay";
};

&esdhc0 {
	status = "okay";
};

&esdhc1 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	fpga@66 {
		compatible = "fsl,lx2160aqds-fpga", "fsl,fpga-qixis-i2c",
			     "simple-mfd";
		reg = <0x66>;

		mux: mux-controller {
			compatible = "reg-mux";
			#mux-control-cells = <1>;
			mux-reg-masks = <0x54 0xf8>, /* 0: reg 0x54, bits 7:3 */
					<0x54 0x07>; /* 1: reg 0x54, bit 2:0 */
		};
	};

	i2c-mux@77 {
		compatible = "nxp,pca9547";
		reg = <0x77>;
		#address-cells = <1>;
		#size-cells = <0>;

		i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x2>;

			power-monitor@40 {
				compatible = "ti,ina220";
				reg = <0x40>;
				shunt-resistor = <500>;
			};

			power-monitor@41 {
				compatible = "ti,ina220";
				reg = <0x41>;
				shunt-resistor = <1000>;
			};
		};

		i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x3>;

			temperature-sensor@4c {
				compatible = "nxp,sa56004";
				reg = <0x4c>;
				vcc-supply = <&sb_3v3>;
			};

			temperature-sensor@4d {
				compatible = "nxp,sa56004";
				reg = <0x4d>;
				vcc-supply = <&sb_3v3>;
			};

			rtc@51 {
				compatible = "nxp,pcf2129";
				reg = <0x51>;
			};
		};
	};
};

&sata0 {
	status = "okay";
};

&sata1 {
	status = "okay";
};

&sata2 {
	status = "okay";
};

&sata3 {
	status = "okay";
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&usb0 {
	status = "okay";
};

&usb1 {
	status = "okay";
};

&sata0 {
	status = "okay";
};

&sata1 {
	status = "okay";
};

&sata2 {
	status = "okay";
};

&sata3 {
	status = "okay";
};
