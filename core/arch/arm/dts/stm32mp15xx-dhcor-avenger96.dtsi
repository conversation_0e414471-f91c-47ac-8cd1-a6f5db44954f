// SPDX-License-Identifier: (GPL-2.0 OR BSD-3-Clause)
/*
 * Copyright (C) Linaro Ltd 2019 - All Rights Reserved
 * Author: <PERSON><PERSON><PERSON><PERSON>has<PERSON> <<EMAIL>>
 * Copyright (C) 2020 Marek Vasut <<EMAIL>>
 */

/* Avenger96 uses DHCOR SoM configured for 1V8 IO operation */
#include "stm32mp15xx-dhcor-io1v8.dtsi"

/ {
	aliases {
		ethernet0 = &ethernet0;
		mmc0 = &sdmmc1;
		serial0 = &uart4;
		serial1 = &uart7;
		serial2 = &usart2;
		spi0 = &qspi;
	};

	/* XTal Q1 */
	cec_clock: clk-cec-fixed {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <24000000>;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	hdmi-out {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con: endpoint {
				remote-endpoint = <&adv7513_out>;
			};
		};
	};

	led {
		compatible = "gpio-leds";
		led1 {
			label = "green:user0";
			gpios = <&gpioz 7 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
			default-state = "off";
		};

		led2 {
			label = "green:user1";
			gpios = <&gpiof 3 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "mmc0";
			default-state = "off";
		};

		led3 {
			label = "green:user2";
			gpios = <&gpiog 0 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "mmc1";
			default-state = "off";
		};

		led4 {
			label = "green:user3";
			gpios = <&gpiog 1 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "none";
			default-state = "off";
			panic-indicator;
		};
	};

	sd_switch: regulator-sd_switch {
		compatible = "regulator-gpio";
		regulator-name = "sd_switch";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <2900000>;
		regulator-type = "voltage";
		regulator-always-on;

		gpios = <&gpioi 5 GPIO_ACTIVE_HIGH>;
		gpios-states = <0>;
		states = <1800000 0x1>,
			 <2900000 0x0>;
	};

	sound {
		compatible = "audio-graph-card";
		label = "STM32MP1-AV96-HDMI";
		dais = <&sai2a_port>;
		status = "okay";
	};

	wlan_pwr: regulator-wlan {
		compatible = "regulator-fixed";

		regulator-name = "wl-reg";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;

		gpios = <&gpioz 3 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};
};

&adc {
	pinctrl-names = "default";
	pinctrl-0 = <&adc12_ain_pins_b>;
	vdd-supply = <&vdd>;
	vdda-supply = <&vdda>;
	vref-supply = <&vdda>;
	status = "okay";

	adc1: adc@0 {
		st,adc-channels = <0 1 6>;
		st,min-sample-time-nsecs = <5000>;
		status = "okay";
	};

	adc2: adc@100 {
		st,adc-channels = <0 1 2>;
		st,min-sample-time-nsecs = <5000>;
		status = "okay";
	};
};

&ethernet0 {
	status = "okay";
	pinctrl-0 = <&ethernet0_rgmii_pins_c>;
	pinctrl-1 = <&ethernet0_rgmii_sleep_pins_c>;
	pinctrl-names = "default", "sleep";
	phy-mode = "rgmii";
	max-speed = <1000>;
	phy-handle = <&phy0>;

	mdio0 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "snps,dwmac-mdio";
		reset-gpios = <&gpioz 2 GPIO_ACTIVE_LOW>;
		reset-delay-us = <1000>;
		reset-post-delay-us = <1000>;

		phy0: ethernet-phy@7 {
			reg = <7>;

			rxc-skew-ps = <1500>;
			rxdv-skew-ps = <540>;
			rxd0-skew-ps = <420>;
			rxd1-skew-ps = <420>;
			rxd2-skew-ps = <420>;
			rxd3-skew-ps = <420>;

			txc-skew-ps = <1440>;
			txen-skew-ps = <540>;
			txd0-skew-ps = <420>;
			txd1-skew-ps = <420>;
			txd2-skew-ps = <420>;
			txd3-skew-ps = <420>;
		};
	};
};

&gpioa {
	gpio-line-names = "", "", "", "",
			  "", "", "", "",
			  "", "", "", "AV96-K",
			  "AV96-I", "", "AV96-A", "";
};

&gpiob {
	gpio-line-names = "", "", "", "",
			  "", "AV96-J", "", "",
			  "", "", "", "AV96-B",
			  "", "AV96-L", "", "";
};

&gpioc {
	gpio-line-names = "", "", "", "AV96-C",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "";
};

&gpiod {
	gpio-line-names = "", "", "", "",
			  "", "", "", "",
			  "AV96-D", "", "", "",
			  "", "", "AV96-E", "AV96-F";
};

&gpiof {
	gpio-line-names = "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "AV96-G", "AV96-H", "", "";
};

&i2c1 {	/* X6 I2C1 */
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pins_b>;
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	status = "okay";
	/delete-property/dmas;
	/delete-property/dma-names;
};

&i2c2 {	/* X6 I2C2 */
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2_pins_c>;
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	status = "okay";
	/delete-property/dmas;
	/delete-property/dma-names;
};

&i2c4 {
	hdmi-transmitter@3d {
		compatible = "adi,adv7513";
		reg = <0x3d>, <0x4d>, <0x2d>, <0x5d>;
		reg-names = "main", "edid", "cec", "packet";
		clocks = <&cec_clock>;
		clock-names = "cec";

		avdd-supply = <&v3v3>;
		dvdd-supply = <&v3v3>;
		pvdd-supply = <&v3v3>;
		dvdd-3v-supply = <&v3v3>;
		bgvdd-supply = <&v3v3>;

		interrupts = <9 IRQ_TYPE_EDGE_FALLING>;
		interrupt-parent = <&gpiog>;

		status = "okay";

		adi,input-depth = <8>;
		adi,input-colorspace = "rgb";
		adi,input-clock = "1x";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				adv7513_in: endpoint {
					remote-endpoint = <&ltdc_ep0_out>;
				};
			};

			port@1 {
				reg = <1>;
				adv7513_out: endpoint {
					remote-endpoint = <&hdmi_con>;
				};
			};

			port@2 {
				reg = <2>;
				adv7513_i2s0: endpoint {
					remote-endpoint = <&sai2a_endpoint>;
				};
			};
		};
	};
};

&ltdc {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&ltdc_pins_d>;
	pinctrl-1 = <&ltdc_sleep_pins_d>;
	status = "okay";

	port {
		#address-cells = <1>;
		#size-cells = <0>;

		ltdc_ep0_out: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&adv7513_in>;
		};
	};
};

&sai2 {
	clocks = <&rcc SAI2>, <&rcc PLL3_Q>, <&rcc PLL3_R>;
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&sai2a_pins_c>;
	pinctrl-1 = <&sai2a_sleep_pins_c>;
	clock-names = "pclk", "x8k", "x11k";
	status = "okay";

	sai2a: audio-controller@4400b004 {
		#clock-cells = <0>;
		dma-names = "tx";
		clocks = <&rcc SAI2_K>;
		clock-names = "sai_ck";
		status = "okay";

		sai2a_port: port {
			sai2a_endpoint: endpoint {
				remote-endpoint = <&adv7513_i2s0>;
				format = "i2s";
				mclk-fs = <256>;
			};
		};
	};
};

&sdmmc1 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a &sdmmc1_dir_pins_b>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a &sdmmc1_dir_pins_b>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a &sdmmc1_dir_sleep_pins_b>;
	cd-gpios = <&gpioi 8 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
	disable-wp;
	st,sig-dir;
	st,neg-edge;
	st,use-ckin;
	bus-width = <4>;
	vmmc-supply = <&vdd_sd>;
	vqmmc-supply = <&sd_switch>;
	status = "okay";
};

&sdmmc2 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc2_b4_pins_a &sdmmc2_d47_pins_c>;
	pinctrl-1 = <&sdmmc2_b4_od_pins_a &sdmmc2_d47_pins_c>;
	pinctrl-2 = <&sdmmc2_b4_sleep_pins_a &sdmmc2_d47_sleep_pins_c>;
	bus-width = <8>;
	mmc-ddr-1_8v;
	no-sd;
	no-sdio;
	non-removable;
	st,neg-edge;
	vmmc-supply = <&v3v3>;
	vqmmc-supply = <&vdd_io>;
	status = "okay";
};

&sdmmc3 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc3_b4_pins_b>;
	pinctrl-1 = <&sdmmc3_b4_od_pins_b>;
	pinctrl-2 = <&sdmmc3_b4_sleep_pins_b>;
	broken-cd;
	non-removable;
	st,neg-edge;
	bus-width = <4>;
	vmmc-supply = <&wlan_pwr>;
	status = "okay";

	#address-cells = <1>;
	#size-cells = <0>;
	brcmf: bcrmf@1 {
		reg = <1>;
		compatible = "brcm,bcm4329-fmac";
	};
};

&spi2 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi2_pins_a>;
	cs-gpios = <&gpioi 0 0>;
	status = "disabled";
	/delete-property/dmas;
	/delete-property/dma-names;
};

&uart4 {
	/* On Low speed expansion header */
	label = "LS-UART1";
	pinctrl-names = "default";
	pinctrl-0 = <&uart4_pins_b>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

&uart7 {
	/* On Low speed expansion header */
	label = "LS-UART0";
	pinctrl-names = "default";
	pinctrl-0 = <&uart7_pins_a>;
	uart-has-rtscts;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

/* Bluetooth */
&usart2 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&usart2_pins_a>;
	pinctrl-1 = <&usart2_sleep_pins_a>;
	st,hw-flow-ctrl;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";

	bluetooth {
		compatible = "brcm,bcm43438-bt";
		max-speed = <3000000>;
		shutdown-gpios = <&gpioz 6 GPIO_ACTIVE_HIGH>;
	};
};

&usbh_ehci {
	phys = <&usbphyc_port0>;
	phy-names = "usb";
	status = "okay";
};

&usbotg_hs {
	pinctrl-0 = <&usbotg_hs_pins_a>;
	pinctrl-names = "default";
	phy-names = "usb2-phy";
	phys = <&usbphyc_port1 0>;
	status = "okay";
	vbus-supply = <&vbus_otg>;
};

&usbphyc {
	status = "okay";
};

&usbphyc_port0 {
	phy-supply = <&vdd_usb>;
};

&usbphyc_port1 {
	phy-supply = <&vdd_usb>;
};
