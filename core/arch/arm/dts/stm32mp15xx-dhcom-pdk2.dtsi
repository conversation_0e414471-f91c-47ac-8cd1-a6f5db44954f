// SPDX-License-Identifier: GPL-2.0+ OR BSD-3-Clause
/*
 * Copyright (C) 2019-2020 <PERSON><PERSON> <<EMAIL>>
 * Copyright (C) 2022 DH electronics GmbH
 */

/ {
	aliases {
		serial0 = &uart4;
		serial1 = &usart3;
		serial2 = &uart8;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	clk_ext_audio_codec: clock-codec {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <********>;
	};

	display_bl: display-bl {
		compatible = "pwm-backlight";
		pwms = <&pwm2 3 500000 1>;
		brightness-levels = <0 16 22 30 40 55 75 102 138 188 255>;
		default-brightness-level = <8>;
		enable-gpios = <&gpioi 0 GPIO_ACTIVE_HIGH>;
		power-supply = <&reg_panel_bl>;
		status = "okay";
	};

	gpio-keys-polled {
		compatible = "gpio-keys-polled";
		poll-interval = <20>;

		/*
		 * The EXTi IRQ line 3 is shared with ethernet,
		 * so mark this as polled GPIO key.
		 */
		button-0 {
			label = "TA1-GPIO-A";
			gpios = <&gpiof 3 GPIO_ACTIVE_LOW>;
		};

		/*
		 * The EXTi IRQ line 6 is shared with touchscreen,
		 * so mark this as polled GPIO key.
		 */
		button-1 {
			label = "TA2-GPIO-B";
			gpios = <&gpiod 6 GPIO_ACTIVE_LOW>;
		};

		/*
		 * The EXTi IRQ line 0 is shared with PMIC,
		 * so mark this as polled GPIO key.
		 */
		button-2 {
			label = "TA3-GPIO-C";
			gpios = <&gpiog 0 GPIO_ACTIVE_LOW>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		button-3 {
			label = "TA4-GPIO-D";
			gpios = <&gpiod 12 GPIO_ACTIVE_LOW>;
			wakeup-source;
		};
	};

	led {
		compatible = "gpio-leds";

		led-0 {
			label = "green:led5";
			gpios = <&gpioc 6 GPIO_ACTIVE_HIGH>;
			default-state = "off";
			status = "disabled";
		};

		led-1 {
			label = "green:led6";
			gpios = <&gpiod 11 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};

		led-2 {
			label = "green:led7";
			gpios = <&gpioi 2 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};

		led-3 {
			label = "green:led8";
			gpios = <&gpioi 3 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};
	};

	panel {
		compatible = "edt,etm0700g0edh6";
		backlight = <&display_bl>;
		power-supply = <&reg_panel_bl>;

		port {
			lcd_panel_in: endpoint {
				remote-endpoint = <&lcd_display_out>;
			};
		};
	};

	reg_panel_bl: regulator-panel-bl {
		compatible = "regulator-fixed";
		regulator-name = "panel_backlight";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&reg_panel_supply>;
	};

	reg_panel_supply: regulator-panel-supply {
		compatible = "regulator-fixed";
		regulator-name = "panel_supply";
		regulator-min-microvolt = <********>;
		regulator-max-microvolt = <********>;
	};

	sound {
		compatible = "audio-graph-card";
		routing =
			"MIC_IN", "Capture",
			"Capture", "Mic Bias",
			"Playback", "HP_OUT";
		dais = <&sai2a_port &sai2b_port>;
		status = "okay";
	};
};

&cec {
	pinctrl-names = "default";
	pinctrl-0 = <&cec_pins_a>;
	status = "okay";
};

&i2c2 {	/* Header X22 */
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2_pins_a>;
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	status = "okay";
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;
};

&i2c5 {	/* Header X21 */
	pinctrl-names = "default";
	pinctrl-0 = <&i2c5_pins_a>;
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	status = "okay";
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;

	sgtl5000: codec@a {
		compatible = "fsl,sgtl5000";
		reg = <0x0a>;
		#sound-dai-cells = <0>;
		clocks = <&clk_ext_audio_codec>;
		VDDA-supply = <&v3v3>;
		VDDIO-supply = <&vdd>;

		sgtl5000_port: port {
			#address-cells = <1>;
			#size-cells = <0>;

			sgtl5000_tx_endpoint: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&sai2a_endpoint>;
				frame-master = <&sgtl5000_tx_endpoint>;
				bitclock-master = <&sgtl5000_tx_endpoint>;
			};

			sgtl5000_rx_endpoint: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&sai2b_endpoint>;
				frame-master = <&sgtl5000_rx_endpoint>;
				bitclock-master = <&sgtl5000_rx_endpoint>;
			};
		};

	};

	touchscreen@38 {
		compatible = "edt,edt-ft5406";
		reg = <0x38>;
		interrupt-parent = <&gpioc>;
		interrupts = <6 IRQ_TYPE_EDGE_FALLING>; /* GPIO E */
	};
};

&ltdc {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&ltdc_pins_b>;
	pinctrl-1 = <&ltdc_sleep_pins_b>;
	status = "okay";

	port {
		lcd_display_out: endpoint {
			remote-endpoint = <&lcd_panel_in>;
		};
	};
};

&sai2 {
	clocks = <&rcc SAI2>, <&rcc PLL3_Q>, <&rcc PLL3_R>;
	clock-names = "pclk", "x8k", "x11k";
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&sai2a_pins_b &sai2b_pins_b>;
	pinctrl-1 = <&sai2a_sleep_pins_b &sai2b_sleep_pins_b>;
	status = "okay";

	sai2a: audio-controller@4400b004 {
		#clock-cells = <0>;
		dma-names = "tx";
		clocks = <&rcc SAI2_K>;
		clock-names = "sai_ck";
		status = "okay";

		sai2a_port: port {
			sai2a_endpoint: endpoint {
				remote-endpoint = <&sgtl5000_tx_endpoint>;
				format = "i2s";
				mclk-fs = <512>;
				dai-tdm-slot-num = <2>;
				dai-tdm-slot-width = <16>;
			};
		};
	};

	sai2b: audio-controller@4400b024 {
		dma-names = "rx";
		st,sync = <&sai2a 2>;
		clocks = <&rcc SAI2_K>, <&sai2a>;
		clock-names = "sai_ck", "MCLK";
		status = "okay";

		sai2b_port: port {
			sai2b_endpoint: endpoint {
				remote-endpoint = <&sgtl5000_rx_endpoint>;
				format = "i2s";
				mclk-fs = <512>;
				dai-tdm-slot-num = <2>;
				dai-tdm-slot-width = <16>;
			};
		};
	};
};

&timers2 {
	/* spare dmas for other usage (un-delete to enable pwm capture) */
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
	pwm2: pwm {
		pinctrl-0 = <&pwm2_pins_a>;
		pinctrl-names = "default";
		status = "okay";
	};
	timer@1 {
		status = "okay";
	};
};

&usart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&usart3_pins_a>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

&uart8 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart8_pins_a &uart8_rtscts_pins_a>;
	uart-has-rtscts;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

&usbh_ehci {
	phys = <&usbphyc_port0>;
	status = "okay";
};

&usbotg_hs {
	dr_mode = "otg";
	pinctrl-0 = <&usbotg_hs_pins_a>;
	pinctrl-names = "default";
	phy-names = "usb2-phy";
	phys = <&usbphyc_port1 0>;
	vbus-supply = <&vbus_otg>;
	status = "okay";
};

&usbphyc {
	status = "okay";
};

&usbphyc_port0 {
	phy-supply = <&vdd_usb>;
};

&usbphyc_port1 {
	phy-supply = <&vdd_usb>;
};
