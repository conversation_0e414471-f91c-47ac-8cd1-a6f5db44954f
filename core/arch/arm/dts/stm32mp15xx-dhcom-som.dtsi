// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) 2019-2020 <PERSON><PERSON> <<EMAIL>>
 * Copyright (C) 2022 DH electronics GmbH
 */

#include "stm32mp15-pinctrl.dtsi"
#include "stm32mp15xxaa-pinctrl.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/mfd/st,stpmic1.h>

/ {
	aliases {
		ethernet0 = &ethernet0;
		ethernet1 = &ksz8851;
		rtc0 = &hwrtc;
		rtc1 = &rtc;
	};

	memory@c0000000 {
		device_type = "memory";
		reg = <0xC0000000 0x40000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		mcuram2: mcuram2@10000000 {
			compatible = "shared-dma-pool";
			reg = <0x10000000 0x40000>;
			no-map;
		};

		vdev0vring0: vdev0vring0@10040000 {
			compatible = "shared-dma-pool";
			reg = <0x10040000 0x1000>;
			no-map;
		};

		vdev0vring1: vdev0vring1@10041000 {
			compatible = "shared-dma-pool";
			reg = <0x10041000 0x1000>;
			no-map;
		};

		vdev0buffer: vdev0buffer@10042000 {
			compatible = "shared-dma-pool";
			reg = <0x10042000 0x4000>;
			no-map;
		};

		mcuram: mcuram@30000000 {
			compatible = "shared-dma-pool";
			reg = <0x30000000 0x40000>;
			no-map;
		};

		retram: retram@38000000 {
			compatible = "shared-dma-pool";
			reg = <0x38000000 0x10000>;
			no-map;
		};
	};

	ethernet_vio: vioregulator {
		compatible = "regulator-fixed";
		regulator-name = "vio";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpiog 3 GPIO_ACTIVE_LOW>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&vdd>;
	};
};

&adc {
	vdd-supply = <&vdd>;
	vdda-supply = <&vdda>;
	vref-supply = <&vdda>;
	status = "okay";

	adc1: adc@0 {
		st,min-sample-time-nsecs = <5000>;
		st,adc-channels = <0>;
		status = "okay";
	};

	adc2: adc@100 {
		st,adc-channels = <1>;
		st,min-sample-time-nsecs = <5000>;
		status = "okay";
	};
};

&crc1 {
	status = "okay";
};

&dac {
	pinctrl-names = "default";
	pinctrl-0 = <&dac_ch1_pins_a &dac_ch2_pins_a>;
	vref-supply = <&vdda>;
	status = "okay";

	dac1: dac@1 {
		status = "okay";
	};
	dac2: dac@2 {
		status = "okay";
	};
};

&dts {
	status = "okay";
};

&ethernet0 {
	status = "okay";
	pinctrl-0 = <&ethernet0_rmii_pins_c &mco2_pins_a>;
	pinctrl-1 = <&ethernet0_rmii_sleep_pins_c &mco2_sleep_pins_a>;
	pinctrl-names = "default", "sleep";
	phy-mode = "rmii";
	max-speed = <100>;
	phy-handle = <&phy0>;

	mdio0 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "snps,dwmac-mdio";

		phy0: ethernet-phy@1 {
			reg = <1>;
			/* LAN8710Ai */
			compatible = "ethernet-phy-id0007.c0f0",
				     "ethernet-phy-ieee802.3-c22";
			clocks = <&rcc CK_MCO2>;
			reset-gpios = <&gpioh 3 GPIO_ACTIVE_LOW>;
			reset-assert-us = <500>;
			reset-deassert-us = <500>;
			smsc,disable-energy-detect;
			interrupt-parent = <&gpioi>;
			interrupts = <11 IRQ_TYPE_LEVEL_LOW>;
		};
	};
};

&fmc {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&fmc_pins_b>;
	pinctrl-1 = <&fmc_sleep_pins_b>;
	status = "okay";

	ksz8851: ethernet@1,0 {
		compatible = "micrel,ks8851-mll";
		reg = <1 0x0 0x2>, <1 0x2 0x20000>;
		interrupt-parent = <&gpioc>;
		interrupts = <3 IRQ_TYPE_LEVEL_LOW>;
		bank-width = <2>;

		/* Timing values are in nS */
		st,fmc2-ebi-cs-mux-enable;
		st,fmc2-ebi-cs-transaction-type = <4>;
		st,fmc2-ebi-cs-buswidth = <16>;
		st,fmc2-ebi-cs-address-setup-ns = <5>;
		st,fmc2-ebi-cs-address-hold-ns = <5>;
		st,fmc2-ebi-cs-bus-turnaround-ns = <5>;
		st,fmc2-ebi-cs-data-setup-ns = <45>;
		st,fmc2-ebi-cs-data-hold-ns = <1>;
		st,fmc2-ebi-cs-write-address-setup-ns = <5>;
		st,fmc2-ebi-cs-write-address-hold-ns = <5>;
		st,fmc2-ebi-cs-write-bus-turnaround-ns = <5>;
		st,fmc2-ebi-cs-write-data-setup-ns = <45>;
		st,fmc2-ebi-cs-write-data-hold-ns = <1>;
	};
};

&gpioa {
	gpio-line-names = "", "", "", "",
			  "", "", "DHCOM-K", "",
			  "", "", "", "",
			  "", "", "", "";
};

&gpiob {
	gpio-line-names = "", "", "", "",
			  "", "", "", "",
			  "DHCOM-Q", "", "", "",
			  "", "", "", "";
};

&gpioc {
	gpio-line-names = "", "", "", "",
			  "", "", "DHCOM-E", "",
			  "", "", "", "",
			  "", "", "", "";
};

&gpiod {
	gpio-line-names = "", "", "", "",
			  "", "", "DHCOM-B", "",
			  "", "", "", "DHCOM-F",
			  "DHCOM-D", "", "", "";
};

&gpioe {
	gpio-line-names = "", "", "", "",
			  "", "", "DHCOM-P", "",
			  "", "", "", "",
			  "", "", "", "";
};

&gpiof {
	gpio-line-names = "", "", "", "DHCOM-A",
			  "", "", "", "",
			  "", "", "", "",
			  "", "", "", "";
};

&gpiog {
	gpio-line-names = "DHCOM-C", "", "", "",
			  "", "", "", "",
			  "DHCOM-L", "", "", "",
			  "", "", "", "";
};

&gpioh {
	gpio-line-names = "", "", "", "",
			  "", "", "", "DHCOM-N",
			  "DHCOM-J", "DHCOM-W", "DHCOM-V", "DHCOM-U",
			  "DHCOM-T", "", "DHCOM-S", "";
};

&gpioi {
	gpio-line-names = "DHCOM-G", "DHCOM-O", "DHCOM-H", "DHCOM-I",
			  "DHCOM-R", "DHCOM-M", "", "",
			  "", "", "", "",
			  "", "", "", "";
};

&i2c4 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c4_pins_a>;
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	status = "okay";
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;

	hwrtc: rtc@32 {
		compatible = "microcrystal,rv8803";
		reg = <0x32>;
	};

	pmic: stpmic@33 {
		compatible = "st,stpmic1";
		reg = <0x33>;
		interrupts-extended = <&gpioa 0 IRQ_TYPE_EDGE_FALLING>;
		interrupt-controller;
		#interrupt-cells = <2>;
		status = "okay";

		regulators {
			compatible = "st,stpmic1-regulators";
			ldo1-supply = <&v3v3>;
			ldo2-supply = <&v3v3>;
			ldo3-supply = <&vdd_ddr>;
			ldo5-supply = <&v3v3>;
			ldo6-supply = <&v3v3>;
			pwr_sw1-supply = <&bst_out>;
			pwr_sw2-supply = <&bst_out>;

			vddcore: buck1 {
				regulator-name = "vddcore";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			vdd_ddr: buck2 {
				regulator-name = "vdd_ddr";
				regulator-min-microvolt = <1350000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			vdd: buck3 {
				regulator-name = "vdd";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				st,mask-reset;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			v3v3: buck4 {
				regulator-name = "v3v3";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-over-current-protection;
				regulator-initial-mode = <0>;
			};

			vdda: ldo1 {
				regulator-name = "vdda";
				regulator-always-on;
				regulator-min-microvolt = <2900000>;
				regulator-max-microvolt = <2900000>;
				interrupts = <IT_CURLIM_LDO1 0>;
			};

			v2v8: ldo2 {
				regulator-name = "v2v8";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				interrupts = <IT_CURLIM_LDO2 0>;
			};

			vtt_ddr: ldo3 {
				regulator-name = "vtt_ddr";
				regulator-min-microvolt = <500000>;
				regulator-max-microvolt = <750000>;
				regulator-always-on;
				regulator-over-current-protection;
			};

			vdd_usb: ldo4 {
				regulator-name = "vdd_usb";
				interrupts = <IT_CURLIM_LDO4 0>;
			};

			vdd_sd: ldo5 {
				regulator-name = "vdd_sd";
				regulator-min-microvolt = <2900000>;
				regulator-max-microvolt = <2900000>;
				interrupts = <IT_CURLIM_LDO5 0>;
				regulator-boot-on;
			};

			v1v8: ldo6 {
				regulator-name = "v1v8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				interrupts = <IT_CURLIM_LDO6 0>;
			};

			vref_ddr: vref_ddr {
				regulator-name = "vref_ddr";
				regulator-always-on;
			};

			bst_out: boost {
				regulator-name = "bst_out";
				interrupts = <IT_OCP_BOOST 0>;
			};

			vbus_otg: pwr_sw1 {
				regulator-name = "vbus_otg";
				interrupts = <IT_OCP_OTG 0>;
			};

			vbus_sw: pwr_sw2 {
				regulator-name = "vbus_sw";
				interrupts = <IT_OCP_SWOUT 0>;
				regulator-active-discharge = <1>;
			};
		};

		onkey {
			compatible = "st,stpmic1-onkey";
			interrupts = <IT_PONKEY_F 0>, <IT_PONKEY_R 0>;
			interrupt-names = "onkey-falling", "onkey-rising";
			power-off-time-sec = <10>;
			status = "okay";
		};

		watchdog {
			compatible = "st,stpmic1-wdt";
			status = "disabled";
		};
	};

	touchscreen@49 {
		compatible = "ti,tsc2004";
		reg = <0x49>;
		vio-supply = <&v3v3>;
		interrupts-extended = <&gpioh 15 IRQ_TYPE_EDGE_FALLING>;
	};

	eeprom@50 {
		compatible = "atmel,24c02";
		reg = <0x50>;
		pagesize = <16>;
	};
};

&ipcc {
	status = "okay";
};

&iwdg2 {
	timeout-sec = <32>;
	status = "okay";
	secure-status = "disabled";
};

&m4_rproc {
	memory-region = <&retram>, <&mcuram>, <&mcuram2>, <&vdev0vring0>,
			<&vdev0vring1>, <&vdev0buffer>;
	mboxes = <&ipcc 0>, <&ipcc 1>, <&ipcc 2>;
	mbox-names = "vq0", "vq1", "shutdown";
	interrupt-parent = <&exti>;
	interrupts = <68 1>;
	status = "okay";
};

&pwr_regulators {
	vdd-supply = <&vdd>;
	vdd_3v3_usbfs-supply = <&vdd_usb>;
};

&qspi {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&qspi_clk_pins_a &qspi_bk1_pins_a>;
	pinctrl-1 = <&qspi_clk_sleep_pins_a &qspi_bk1_sleep_pins_a>;
	reg = <0x58003000 0x1000>, <0x70000000 0x4000000>;
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	flash0: flash@0 {
		compatible = "jedec,spi-nor";
		reg = <0>;
		spi-rx-bus-width = <4>;
		spi-max-frequency = <108000000>;
		#address-cells = <1>;
		#size-cells = <1>;
	};
};

&rcc {
	/* Connect MCO2 output to ETH_RX_CLK input via pad-pad connection */
	/* clocks = <&rcc CK_MCO2>; Not supported in OP-TEE OS */
	/* clock-names = "ETH_RX_CLK/ETH_REF_CLK"; Not supported */

	/*
	 * Set PLL4P output to 100 MHz to supply SDMMC with faster clock,
	 * set MCO2 output to 50 MHz to supply ETHRX clock with PLL4P/2,
	 * so that MCO2 behaves as a divider for the ETHRX clock here.
	 */
	/* assigned-clocks = <&rcc CK_MCO2>, <&rcc PLL4_P>; Not supported */
	/* assigned-clock-parents = <&rcc PLL4_P>; Not supported */
	/* assigned-clock-rates = <50000000>, <100000000>; Not supported */

	status = "okay";
};

&rng1 {
	status = "okay";
};

&rtc {
	status = "okay";
};

&sdmmc1 {
	pinctrl-names = "default", "opendrain", "sleep", "init";
	pinctrl-0 = <&sdmmc1_b4_pins_a &sdmmc1_dir_pins_a>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a &sdmmc1_dir_pins_a>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a &sdmmc1_dir_sleep_pins_a>;
	pinctrl-3 = <&sdmmc1_b4_init_pins_a &sdmmc1_dir_init_pins_a>;
	cd-gpios = <&gpiog 1 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
	disable-wp;
	st,sig-dir;
	st,neg-edge;
	st,use-ckin;
	st,cmd-gpios = <&gpiod 2 0>;
	st,ck-gpios = <&gpioc 12 0>;
	st,ckin-gpios = <&gpioe 4 0>;
	bus-width = <4>;
	vmmc-supply = <&vdd_sd>;
	status = "okay";
};

&sdmmc1_b4_pins_a {
	/*
	 * SD bus pull-up resistors:
	 * - optional on SoMs with SD voltage translator
	 * - mandatory on SoMs without SD voltage translator
	 */
	pins1 {
		bias-pull-up;
	};
	pins2 {
		bias-pull-up;
	};
};

&sdmmc2 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc2_b4_pins_a &sdmmc2_d47_pins_a>;
	pinctrl-1 = <&sdmmc2_b4_od_pins_a &sdmmc2_d47_pins_a>;
	pinctrl-2 = <&sdmmc2_b4_sleep_pins_a &sdmmc2_d47_sleep_pins_a>;
	non-removable;
	no-sd;
	no-sdio;
	st,neg-edge;
	bus-width = <8>;
	vmmc-supply = <&v3v3>;
	vqmmc-supply = <&v3v3>;
	mmc-ddr-3_3v;
	status = "okay";
};

&sdmmc3 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc3_b4_pins_a>;
	pinctrl-1 = <&sdmmc3_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc3_b4_sleep_pins_a>;
	broken-cd;
	st,neg-edge;
	bus-width = <4>;
	vmmc-supply = <&v3v3>;
	vqmmc-supply = <&v3v3>;
	mmc-ddr-3_3v;
	status = "okay";
};

&uart4 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart4_pins_a>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};
