// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2019 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>> for STMicroelectronics.
 */

#include "stm32mp153.dtsi"

/ {
	soc {
		gpu: gpu@59000000 {
			compatible = "vivante,gc";
			reg = <0x59000000 0x800>;
			interrupts = <GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc GPU>, <&rcc GPU_K>;
			clock-names = "bus" ,"core";
			resets = <&rcc GPU_R>;
		};

		dsi: dsi@5a000000 {
			compatible = "st,stm32-dsi";
			reg = <0x5a000000 0x800>;
			clocks = <&rcc DSI_K>, <&clk_hse>, <&rcc DSI_PX>;
			clock-names = "pclk", "ref", "px_clk";
			resets = <&rcc DSI_R>;
			reset-names = "apb";
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
	};
};
