CFG_IMX_ECSPI = y
CFG_RSEE_DEV_STATUS = n
CFG_ROCKY_TEE_RPMB_PROBE = y

#CFG_REE_FS_ALLOW_RESET = y
$(call force,CFG_REE_FS_ALLOW_RESET,y)

# enable RPMB file system support
CFG_RPMB_FS = y

#add the functionality of detecting tee files during factory reset
CFG_TEE_FS_CHECK = y

CFG_CORE_HUK_SUBKEY_COMPAT = y

# SSK key derivations used Chip DIE_ID instead of a fixed string(pattern[4] = { 'B', 'E', 'E', 'F' })
CFG_IMX_OCOTP = y
CFG_CORE_HUK_SUBKEY_COMPAT_USE_OTP_DIE_ID  = y

#define the emmc/ufs CID in the Linux Kernel
#like :
#	/sys/class/mmc_host/mmc2/mmc2:0001/cid
#because in the imx8mp evk board, the mmc1 is used for SD card,so the ID：2
CFG_RPMB_FS_DEV_ID = 2

CFG_TEE_VERSION_READ = y

ifeq ($(CFG_TEE_VERSION_READ),y)
#get latest git commit ID
git_ver=$(shell git log --pretty=oneline -1 |cut -c 1-8)

ifneq ($(git_ver),)
	CFLAGS += -DGIT_VER=\"\$(git_ver)\"
	CXXFLAGS += -DGIT_VER=\"\$(git_ver)\"
endif
endif

#hw_unique_key support
CFG_HW_UNQ_KEY_SUPPORT = y

# TEE OS build with gatekeeper and keymaster ETA
ifeq ($(CFG_EARLY_TA),y)
ifeq ($(CFG_GATEKEEPER_TA),y)
	CFG_IN_TREE_EARLY_TAS += gatekeeper/b511ba17-b3b3-47f5-b389-6b53f30c0baf
endif
ifeq ($(CFG_KEYMASTER_TA),y)
	CFG_IN_TREE_EARLY_TAS += keymaster/b657ba17-b3b3-47f5-b389-6b53f30c0baf
endif
endif

#tee share log,default is n
#if use tee share log function(CFG_SHM_LOG=y),CFG_TEE_VERSION_READ must be set to "y"
#CFG_SHM_LOG = y
CFG_CORE_RESERVED_SHM = y

CFG_TEE_STORAGE_PERSIST_REE_SUPPORT =y

ifeq ($(TEE_BUILD_VARIANT),debug)
	CFG_ROCKYTEE_OS_TEST_PTA = y
	CFG_TA_STATS = y
	CFG_WITH_STATS = y
endif