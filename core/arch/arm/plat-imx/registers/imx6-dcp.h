/* SPDX-License-Identifier: BSD-2-Clause */
/*
 * Copyright 2020 NXP
 */

#ifndef __IMX6_DCP_H__
#define __IMX6_DCP_H__

/* DCP base address */
#ifdef CFG_MX6ULL
#define DCP_BASE 0x2280000
#endif
#if defined(CFG_MX6SL) || defined(CFG_MX6SLL)
#define DCP_BASE 0x20FC000
#endif

/* DCP registers address offset */
#define DCP_CTRL	   0x00
#define DCP_CTRL_SET	   0x04
#define DCP_CTRL_CLR	   0x08
#define DCP_STAT	   0x10
#define DCP_STAT_CLR	   0x18
#define DCP_CHANNELCTRL	   0x20
#define DCP_CAPABILITY0	   0x30
#define DCP_CAPABILITY1	   0x40
#define DCP_CONTEXT	   0x50
#define DCP_KEY		   0x60
#define DCP_KEYDATA	   0x70
#define DCP_PACKET0	   0x80
#define DCP_PACKET1	   0x90
#define DCP_PACKET2	   0xA0
#define DCP_PACKET3	   0xB0
#define DCP_PACKET4	   0xC0
#define DCP_PACKET5	   0xD0
#define DCP_PACKET6	   0xE0
#define DCP_CH_N_CMDPTR(n) (0x100 + (n) * 0x40)
#define DCP_CH_N_SEMA(n)   (0x110 + (n) * 0x40)
#define DCP_CH_N_STAT(n)   (0x120 + (n) * 0x40)
#define DCP_CHOCMDPTR	   0x100
#define DCP_CH0SEMA	   0x110
#define DCP_CH0STAT	   0x120
#define DCP_CH1CMDPTR	   0x140
#define DCP_CH2CMDPTR	   0x180
#define DCP_CH3CMDPTR	   0x1C0

/* DCP CHANNELCTRL register configuration */
#define DCP_CHANNELCTRL_ENABLE_CHANNEL_MASK GENMASK_32(7, 0)
#define DCP_STAT_CLEAR			    GENMASK_32(31, 0)
#define DCP_CH_STAT_ERROR_MASK		    GENMASK_32(23, 0)

/* DCP CTRL register configuration */
#define DCP_CTRL_SFTRST			  BIT32(31)
#define DCP_CTRL_CLKGATE		  BIT32(30)
#define DCP_CTRL_GATHER_RESIDUAL_WRITES	  BIT32(23)
#define DCP_CTRL_ENABLE_CONTEXT_CACHING	  BIT32(22)
#define DCP_CTRL_ENABLE_CONTEXT_SWITCHING BIT32(21)
#define DCP_CTRL_CH3_INTERRUPT_ENABLE	  BIT32(3)
#define DCP_CTRL_CH2_INTERRUPT_ENABLE	  BIT32(2)
#define DCP_CTRL_CH1_INTERRUPT_ENABLE	  BIT32(1)
#define DCP_CTRL_CH0_INTERRUPT_ENABLE	  BIT32(0)

/* DCP CAPABILITY0 register configuration */
#define DCP_CAPABILITY0_DISABLE_UNIQUE_KEY BIT32(29)

/* Work Packet control0 configuration */
#define DCP_CONTROL0_OUTPUT_WORDSWAP  BIT32(23)
#define DCP_CONTROL0_OUTPUT_BYTESWAP  BIT32(22)
#define DCP_CONTROL0_INPUT_WORDSWAP   BIT32(21)
#define DCP_CONTROL0_INPUT_BYTESWAP   BIT32(20)
#define DCP_CONTROL0_KEY_WORDSWAP     BIT32(19)
#define DCP_CONTROL0_KEY_BYTESWA      BIT32(18)
#define DCP_CONTROL0_TEST_SEMA_IRQ    BIT32(17)
#define DCP_CONTROL0_CONSTANT_FILL    BIT32(16)
#define DCP_CONTROL0_HASH_OUTPUT      BIT32(15)
#define DCP_CONTROL0_HASH_CHECK	      BIT32(14)
#define DCP_CONTROL0_HASH_TERM	      BIT32(13)
#define DCP_CONTROL0_HASH_INIT	      BIT32(12)
#define DCP_CONTROL0_PAYLOAD_KEY      BIT32(11)
#define DCP_CONTROL0_OTP_KEY	      BIT32(10)
#define DCP_CONTROL0_CIPHER_INIT      BIT32(9)
#define DCP_CONTROL0_CIPHER_ENCRYPT   BIT32(8)
#define DCP_CONTROL0_ENABLE_BLIT      BIT32(7)
#define DCP_CONTROL0_ENABLE_HASH      BIT32(6)
#define DCP_CONTROL0_ENABLE_CIPHER    BIT32(5)
#define DCP_CONTROL0_ENABLE_MEMCOPY   BIT32(4)
#define DCP_CONTROL0_CHAIN_CONTINUOUS BIT32(3)
#define DCP_CONTROL0_CHAIN	      BIT32(2)
#define DCP_CONTROL0_DECR_SEMAPHORE   BIT32(1)
#define DCP_CONTROL0_INTERRUPT_ENABLE BIT32(0)

/* Work Packet control1 configuration */
#define DCP_CONTROL1_HASH_SELECT_SHA256	   SHIFT_U32(2, 16)
#define DCP_CONTROL1_HASH_SELECT_CRC32	   BIT32(16)
#define DCP_CONTROL1_HASH_SELECT_SHA1	   SHIFT_U32(0, 16)
#define DCP_CONTROL1_CIPHER_MODE_CBC	   BIT32(4)
#define DCP_CONTROL1_CIPHER_MODE_ECB	   SHIFT_U32(0, 4)
#define DCP_CONTROL1_CIPHER_SELECT_AES128  0
#define DCP_CONTROL1_KEY_SELECT_OTP_CRYPTO SHIFT_U32(0xfe, 8)

#endif /* __IMX6_DCP_H__ */
