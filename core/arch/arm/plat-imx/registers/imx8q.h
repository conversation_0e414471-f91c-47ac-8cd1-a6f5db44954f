/* SPDX-License-Identifier: BSD-2-Clause */
/*
 * Copyright 2018-2021 NXP
 */

#ifndef __IMX8Q_H__
#define __IMX8Q_H__

#define GICD_BASE	0x51a00000
#define GICR_BASE	0x51b00000
#define UART0_BASE	0x5a060000
#define UART1_BASE	0x5a070000
#define UART2_BASE	0x5a080000
#define UART3_BASE	0x5a090000
#define UART4_BASE	0x5a0a0000
#define CAAM_BASE	0x31400000
#define CAAM_SIZE       0x40000
#define SC_IPC0_BASE	0x5d1b0000
#define SC_IPC3_BASE	0x5d1e0000
#define SC_IPC_SIZE	   0x10000

#endif /* __IMX8Q_H__ */
