/* SPDX-License-Identifier: BSD-2-Clause */
/*
 * Copyright (c) 2015, Linaro Limited
 */

#ifndef PLATFORM_CONFIG_H
#define PLATFORM_CONFIG_H

#include <mm/generic_ram_layout.h>

/* Make stacks aligned to data cache line length */
#define STACK_ALIGNMENT		64

#ifdef ARM64
#ifdef CFG_WITH_PAGER
#error "Pager not supported for ARM64"
#endif
#endif /*ARM64*/

#if defined(PLATFORM_FLAVOR_mt8173)

#define GIC_BASE		0x10220000
#define GICC_OFFSET		0x2000
#define GICD_OFFSET		0x1000

#define UART0_BASE		0x11002000
#define UART1_BASE		0x11003000
#define UART2_BASE		0x11004000
#define UART3_BASE		0x11005000

#define CONSOLE_UART_BASE	UART0_BASE
#define CONSOLE_BAUDRATE	921600
#define CONSOLE_UART_CLK_IN_HZ	26000000

#define DRAM0_BASE		0x40000000
#define DRAM0_SIZE		0x80000000

#elif defined(PLATFORM_FLAVOR_mt8175)

#define GIC_BASE		0x0C000000
#define GICC_OFFSET		0x400000
#define GICD_OFFSET		0x0

#define UART0_BASE		0x11002000
#define UART1_BASE		0x11103000
#define UART2_BASE		0x11104000

#define CONSOLE_UART_BASE	UART0_BASE
#define CONSOLE_BAUDRATE	921600
#define CONSOLE_UART_CLK_IN_HZ	26000000

#elif defined(PLATFORM_FLAVOR_mt8516)

#define GIC_BASE		0x10310000
#define GICC_OFFSET		0x10000
#define GICD_OFFSET		0x00000

#define UART0_BASE		0x11005000
#define UART1_BASE		0x11106000
#define UART2_BASE		0x11107000

#define CONSOLE_UART_BASE	UART0_BASE
#define CONSOLE_BAUDRATE	921600
#define CONSOLE_UART_CLK_IN_HZ	26000000

#elif defined(PLATFORM_FLAVOR_mt8183)

#define GIC_BASE		0x0C000000
#define GICC_OFFSET		0x400000
#define GICD_OFFSET		0x0

#define UART0_BASE		0x11002000
#define UART1_BASE		0x11103000
#define UART2_BASE		0x11104000

#define CONSOLE_UART_BASE	UART0_BASE
#define CONSOLE_BAUDRATE	921600
#define CONSOLE_UART_CLK_IN_HZ	26000000

#else
#error "Unknown platform flavor"
#endif

#ifdef CFG_WITH_LPAE
#define MAX_XLAT_TABLES		5
#endif

#endif /*PLATFORM_CONFIG_H*/
