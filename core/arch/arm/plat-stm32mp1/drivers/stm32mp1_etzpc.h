/* SPDX-License-Identifier: GPL-2.0 or BSD-3-Clause */
/*
 * Copyright (C) 2018-2019, STMicroelectronics
 */

#ifndef __STM32MP1_ETZPC_H
#define __STM32MP1_ETZPC_H

/* Define DECPROT IDs for stm32mp1 familly */
#ifdef CFG_STM32MP15
#define STM32MP1_ETZPC_STGENC_ID	0
#define STM32MP1_ETZPC_BKPSRAM_ID	1
#define STM32MP1_ETZPC_IWDG1_ID		2
#define STM32MP1_ETZPC_USART1_ID	3
#define STM32MP1_ETZPC_SPI6_ID		4
#define STM32MP1_ETZPC_I2C4_ID		5
#define STM32MP1_ETZPC_GPIOZ_ID		6
#define STM32MP1_ETZPC_RNG1_ID		7
#define STM32MP1_ETZPC_HASH1_ID		8
#define STM32MP1_ETZPC_CRYP1_ID		9
#define STM32MP1_ETZPC_DDRCTRL_ID	10
#define STM32MP1_ETZPC_DDRPHYC_ID	11
#define STM32MP1_ETZPC_I2C6_ID		12
/* 13-15 Reserved */
#define STM32MP1_ETZPC_TIM2_ID		16
#define STM32MP1_ETZPC_TIM3_ID		17
#define STM32MP1_ETZPC_TIM4_ID		18
#define STM32MP1_ETZPC_TIM5_ID		19
#define STM32MP1_ETZPC_TIM6_ID		20
#define STM32MP1_ETZPC_TIM7_ID		21
#define STM32MP1_ETZPC_TIM12_ID		22
#define STM32MP1_ETZPC_TIM13_ID		23
#define STM32MP1_ETZPC_TIM14_ID		24
#define STM32MP1_ETZPC_LPTIM1_ID	25
#define STM32MP1_ETZPC_WWDG1_ID		26
#define STM32MP1_ETZPC_SPI2_ID		27
#define STM32MP1_ETZPC_SPI3_ID		28
#define STM32MP1_ETZPC_SPDIFRX_ID	29
#define STM32MP1_ETZPC_USART2_ID	30
#define STM32MP1_ETZPC_USART3_ID	31
#define STM32MP1_ETZPC_UART4_ID		32
#define STM32MP1_ETZPC_UART5_ID		33
#define STM32MP1_ETZPC_I2C1_ID		34
#define STM32MP1_ETZPC_I2C2_ID		35
#define STM32MP1_ETZPC_I2C3_ID		36
#define STM32MP1_ETZPC_I2C5_ID		37
#define STM32MP1_ETZPC_CEC_ID		38
#define STM32MP1_ETZPC_DAC_ID		39
#define STM32MP1_ETZPC_UART7_ID		40
#define STM32MP1_ETZPC_UART8_ID		41
/* 42-43 Reserved */
#define STM32MP1_ETZPC_MDIOS_ID		44
/* 45-47 Reserved */
#define STM32MP1_ETZPC_TIM1_ID		48
#define STM32MP1_ETZPC_TIM8_ID		49
/* 50 Reserved */
#define STM32MP1_ETZPC_USART6_ID	51
#define STM32MP1_ETZPC_SPI1_ID		52
#define STM32MP1_ETZPC_SPI4_ID		53
#define STM32MP1_ETZPC_TIM15_ID		54
#define STM32MP1_ETZPC_TIM16_ID		55
#define STM32MP1_ETZPC_TIM17_ID		56
#define STM32MP1_ETZPC_SPI5_ID		57
#define STM32MP1_ETZPC_SAI1_ID		58
#define STM32MP1_ETZPC_SAI2_ID		59
#define STM32MP1_ETZPC_SAI3_ID		60
#define STM32MP1_ETZPC_DFSDM_ID		61
#define STM32MP1_ETZPC_TT_FDCAN_ID	62
/* 63 Reserved */
#define STM32MP1_ETZPC_LPTIM2_ID	64
#define STM32MP1_ETZPC_LPTIM3_ID	65
#define STM32MP1_ETZPC_LPTIM4_ID	66
#define STM32MP1_ETZPC_LPTIM5_ID	67
#define STM32MP1_ETZPC_SAI4_ID		68
#define STM32MP1_ETZPC_VREFBUF_ID	69
#define STM32MP1_ETZPC_DCMI_ID		70
#define STM32MP1_ETZPC_CRC2_ID		71
#define STM32MP1_ETZPC_ADC_ID		72
#define STM32MP1_ETZPC_HASH2_ID		73
#define STM32MP1_ETZPC_RNG2_ID		74
#define STM32MP1_ETZPC_CRYP2_ID		75
/* 76-79 Reserved */
#define STM32MP1_ETZPC_SRAM1_ID		80
#define STM32MP1_ETZPC_SRAM2_ID		81
#define STM32MP1_ETZPC_SRAM3_ID		82
#define STM32MP1_ETZPC_SRAM4_ID		83
#define STM32MP1_ETZPC_RETRAM_ID	84
#define STM32MP1_ETZPC_OTG_ID		85
#define STM32MP1_ETZPC_SDMMC3_ID	86
#define STM32MP1_ETZPC_DLYBSD3_ID	87
#define STM32MP1_ETZPC_DMA1_ID		88
#define STM32MP1_ETZPC_DMA2_ID		89
#define STM32MP1_ETZPC_DMAMUX_ID	90
#define STM32MP1_ETZPC_FMC_ID		91
#define STM32MP1_ETZPC_QSPI_ID		92
#define STM32MP1_ETZPC_DLYBQ_ID		93
#define STM32MP1_ETZPC_ETH_ID		94
/* 95 Reserved */
#define STM32MP1_ETZPC_MAX_ID		96
#endif /* CFG_STM32MP15 */

#ifdef CFG_STM32MP13
#define STM32MP1_ETZPC_VREFBUF_ID	0
#define STM32MP1_ETZPC_LPTIM2_ID	1
#define STM32MP1_ETZPC_LPTIM3_ID	2
#define STM32MP1_ETZPC_LTDC_ID		3
#define STM32MP1_ETZPC_DCMIPP_ID	4
#define STM32MP1_ETZPC_USBPHYCTRL_ID	5
#define STM32MP1_ETZPC_DDRCTRLPHY_ID	6
/* 7-11 Reserved */
#define STM32MP1_ETZPC_IWDG1_ID		12
#define STM32MP1_ETZPC_STGENC_ID	13
/* 14-15 Reserved */
#define STM32MP1_ETZPC_USART1_ID	16
#define STM32MP1_ETZPC_USART2_ID	17
#define STM32MP1_ETZPC_SPI4_ID		18
#define STM32MP1_ETZPC_SPI5_ID		19
#define STM32MP1_ETZPC_I2C3_ID		20
#define STM32MP1_ETZPC_I2C4_ID		21
#define STM32MP1_ETZPC_I2C5_ID		22
#define STM32MP1_ETZPC_TIM12_ID		23
#define STM32MP1_ETZPC_TIM13_ID		24
#define STM32MP1_ETZPC_TIM14_ID		25
#define STM32MP1_ETZPC_TIM15_ID		26
#define STM32MP1_ETZPC_TIM16_ID		27
#define STM32MP1_ETZPC_TIM17_ID		28
/* 29-31 Reserved */
#define STM32MP1_ETZPC_ADC1_ID		32
#define STM32MP1_ETZPC_ADC2_ID		33
#define STM32MP1_ETZPC_OTG_ID		34
/* 35-36 Reserved */
#define STM32MP1_ETZPC_TSC_ID		37
/* 38-39 Reserved */
#define STM32MP1_ETZPC_RNG_ID		40
#define STM32MP1_ETZPC_HASH_ID		41
#define STM32MP1_ETZPC_CRYP_ID		42
#define STM32MP1_ETZPC_SAES_ID		43
#define STM32MP1_ETZPC_PKA_ID		44
#define STM32MP1_ETZPC_BKPSRAM_ID	45
/* 46-47 Reserved */
#define STM32MP1_ETZPC_ETH1_ID		48
#define STM32MP1_ETZPC_ETH2_ID		49
#define STM32MP1_ETZPC_SDMMC1_ID	50
#define STM32MP1_ETZPC_SDMMC2_ID	51
/* 52 Reserved */
#define STM32MP1_ETZPC_MCE_ID		53
#define STM32MP1_ETZPC_FMC_ID		54
#define STM32MP1_ETZPC_QSPI_ID		55
/* 56-59 Reserved */
#define STM32MP1_ETZPC_SRAM1_ID		60
#define STM32MP1_ETZPC_SRAM2_ID		61
#define STM32MP1_ETZPC_SRAM3_ID		62
/* 63 Reserved */
#define STM32MP1_ETZPC_MAX_ID		64
#endif /* CFG_STM32MP13 */
#endif /*__STM32MP1_ETZPC_H*/
