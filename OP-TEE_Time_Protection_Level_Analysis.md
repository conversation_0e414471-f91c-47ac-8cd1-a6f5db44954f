# OP-TEE系统时间保护级别详细分析

## 1. 概述

本文档详细解释OP-TEE中如何确定和查询系统时间的保护级别，包括保护级别的定义、确定机制、查询方法以及不同硬件平台的实现。

## 2. 保护级别的定义和含义

### 2.1 GP规范中的保护级别定义

根据GlobalPlatform TEE规范，时间保护级别定义了时间源的安全等级：

- **保护级别100**：基于REE控制的实时时钟和TEE可信存储
- **保护级别1000**：基于TEE控制的实时时钟和TEE可信存储，该实时时钟必须不受REE软件攻击的影响

### 2.2 OP-TEE中的保护级别实现

**系统时间保护级别**：
```c
// core/arch/arm/kernel/tee_time_arm_cntpct.c - ARM通用计时器实现
uint32_t tee_time_get_sys_time_protection_level(void)
{
    return 1000;  // TEE控制的硬件定时器
}

// core/kernel/tee_time_ree.c - REE时间实现
uint32_t tee_time_get_sys_time_protection_level(void)
{
    return 100;   // REE控制的定时器
}
```

**TA持久时间保护级别**：
```c
// core/tee/tee_svc.c
/*
 * TA persistent time protection level
 * 100: Persistent time based on an REE-controlled real-time clock
 * and on the TEE Trusted Storage for the storage of origins (default).
 * 1000: Persistent time based on a TEE-controlled real-time clock
 * and the TEE Trusted Storage.
 * The real-time clock MUST be out of reach of software attacks
 * from the REE.
 */
static const uint32_t ta_time_prot_lvl = 100;
```

### 2.3 保护级别与时间源可信度的关系

| 保护级别 | 时间源类型 | 安全特性 | 攻击抵抗能力 |
|---------|-----------|----------|-------------|
| 100 | REE控制的RTC + TEE存储 | 基础保护 | 可能受REE软件攻击影响 |
| 1000 | TEE控制的硬件计时器 + TEE存储 | 高级保护 | 不受REE软件攻击影响 |

## 3. 保护级别的确定机制

### 3.1 编译时配置机制

OP-TEE通过编译时配置来选择时间源实现：

**配置选项**：
```makefile
# core/arch/arm/kernel/sub.mk
srcs-$(CFG_SECURE_TIME_SOURCE_CNTPCT) += tee_time_arm_cntpct.c
```

**平台配置示例**：
```makefile
# core/arch/arm/plat-sunxi/conf.mk
$(call force,CFG_SECURE_TIME_SOURCE_CNTPCT,y)

# core/arch/arm/plat-ti/conf.mk
$(call force,CFG_SECURE_TIME_SOURCE_CNTPCT,y)  # dra7xx, am57xx
$(call force,CFG_SECURE_TIME_SOURCE_REE,y)     # am43xx
```

### 3.2 时间源选择逻辑

OP-TEE根据以下优先级选择时间源：

1. **硬件计时器优先**：如果配置了`CFG_SECURE_TIME_SOURCE_CNTPCT`，使用ARM通用计时器
2. **专用硬件计时器**：如Atmel TCB等专用硬件
3. **REE时间回退**：如果没有安全硬件计时器，使用REE时间

### 3.3 运行时检测机制

**Atmel TCB时间源注册**：
```c
// core/drivers/atmel_tcb.c
static const struct time_source atmel_tcb_time_source = {
    .name = "atmel_tcb",
    .protection_level = 1000,
    .get_sys_time = atmel_tcb_get_sys_time,
};

REGISTER_TIME_SOURCE(atmel_tcb_time_source)
```

这种机制允许在运行时动态注册时间源，系统会自动选择最高保护级别的可用时间源。

## 4. 保护级别的查询方法

### 4.1 内核级查询接口

**直接查询函数**：
```c
// core/include/kernel/tee_time.h
uint32_t tee_time_get_sys_time_protection_level(void);
```

**系统调用层实现**：
```c
// core/tee/tee_svc.c
static TEE_Result
get_prop_tee_sys_time_prot_level(struct ts_session *sess __unused,
                                 void *buf, size_t *blen)
{
    uint32_t prot;

    if (*blen < sizeof(prot)) {
        *blen = sizeof(prot);
        return TEE_ERROR_SHORT_BUFFER;
    }
    *blen = sizeof(prot);
    prot = tee_time_get_sys_time_protection_level();
    return copy_to_user(buf, &prot, sizeof(prot));
}
```

### 4.2 GP规范属性查询接口

**属性定义**：
```c
// core/tee/tee_svc.c
/* Properties of the set TEE_PROPSET_TEE_IMPLEMENTATION */
const struct tee_props tee_propset_tee[] = {
    {
        .name = "gpd.tee.systemTime.protectionLevel",
        .prop_type = USER_TA_PROP_TYPE_U32,
        .get_prop_func = get_prop_tee_sys_time_prot_level
    },
    {
        .name = "gpd.tee.TAPersistentTime.protectionLevel",
        .prop_type = USER_TA_PROP_TYPE_U32,
        .data = &ta_time_prot_lvl,
        .len = sizeof(ta_time_prot_lvl)
    },
    // ... 其他属性
};
```

### 4.3 TA应用查询方法

**查询系统时间保护级别**：
```c
// TA应用代码示例
TEE_Result query_system_time_protection_level(uint32_t *protection_level)
{
    TEE_Result res;
    uint32_t level;

    res = TEE_GetPropertyAsU32(TEE_PROPSET_TEE_IMPLEMENTATION,
                               "gpd.tee.systemTime.protectionLevel",
                               &level);
    if (res == TEE_SUCCESS) {
        *protection_level = level;
        DMSG("System time protection level: %u", level);
    }

    return res;
}
```

**查询TA持久时间保护级别**：
```c
// TA应用代码示例
TEE_Result query_ta_persistent_time_protection_level(uint32_t *protection_level)
{
    TEE_Result res;
    uint32_t level;

    res = TEE_GetPropertyAsU32(TEE_PROPSET_TEE_IMPLEMENTATION,
                               "gpd.tee.TAPersistentTime.protectionLevel",
                               &level);
    if (res == TEE_SUCCESS) {
        *protection_level = level;
        DMSG("TA persistent time protection level: %u", level);
    }

    return res;
}
```

## 5. 不同硬件平台的保护级别实现

### 5.1 ARM通用计时器实现（保护级别1000）

**实现原理**：
```c
// core/arch/arm/kernel/tee_time_arm_cntpct.c
TEE_Result tee_time_get_sys_time(TEE_Time *time)
{
    uint64_t cntpct = barrier_read_counter_timer();
    uint32_t cntfrq = read_cntfrq();

    time->seconds = cntpct / cntfrq;
    time->millis = (cntpct % cntfrq) / (cntfrq / TEE_TIME_MILLIS_BASE);

    return TEE_SUCCESS;
}

uint32_t tee_time_get_sys_time_protection_level(void)
{
    return 1000;
}
```

**为什么返回1000级别**：
1. **硬件控制**：ARM通用计时器由硬件直接控制，不依赖软件
2. **安全访问**：计时器寄存器只能在安全世界访问
3. **不可篡改**：REE无法修改计时器的计数值和频率
4. **单调性保证**：硬件保证计数器单调递增

### 5.2 Atmel TCB实现（保护级别1000）

**实现原理**：
```c
// core/drivers/atmel_tcb.c
static TEE_Result atmel_tcb_get_sys_time(TEE_Time *time)
{
    uint64_t cv0 = 0;
    uint64_t cv1 = 0;

    if (!tcb_base)
        return TEE_ERROR_BAD_STATE;

    do {
        cv1 = io_read32(tcb_base + TCB_CV(1));
        cv0 = io_read32(tcb_base + TCB_CV(0));
    } while (io_read32(tcb_base + TCB_CV(1)) != cv1);

    cv0 |= cv1 << 32;

    time->seconds = cv0 / tcb_rate;
    time->millis = (cv0 % tcb_rate) / (tcb_rate / TEE_TIME_MILLIS_BASE);

    return TEE_SUCCESS;
}

static const struct time_source atmel_tcb_time_source = {
    .name = "atmel_tcb",
    .protection_level = 1000,
    .get_sys_time = atmel_tcb_get_sys_time,
};
```

**为什么返回1000级别**：
1. **专用硬件**：TCB是专用的硬件计时器块
2. **64位计数器**：通过链接两个32位通道实现64位计数
3. **低功耗时钟**：使用32KHz慢时钟，在低功耗模式下继续运行
4. **硬件保护**：计时器配置受硬件写保护机制保护

### 5.3 REE时间实现（保护级别100）

**实现原理**：
```c
// core/kernel/tee_time_ree.c
TEE_Result tee_time_get_sys_time(TEE_Time *time)
{
    TEE_Result res;

    res = tee_time_get_ree_time(time);
    if (res != TEE_SUCCESS)
        return res;

    mutex_lock(&time_mu);
    if (time->seconds < prev.seconds ||
        (time->seconds == prev.seconds &&
         time->millis < prev.millis))
        *time = prev; /* REE time was rolled back */
    else
        prev = *time;
    mutex_unlock(&time_mu);

    return res;
}

uint32_t tee_time_get_sys_time_protection_level(void)
{
    return 100;
}
```

**为什么返回100级别**：
1. **REE控制**：时间源来自REE，可能被REE软件修改
2. **RPC依赖**：通过RPC机制获取时间，存在通信开销
3. **安全限制**：无法保证时间的绝对可信性
4. **回滚风险**：虽然有防回滚机制，但在系统重启后失效

## 6. 查询结果的使用场景和安全考量

### 6.1 使用场景

**1. 安全策略决策**：
```c
// TA应用示例：根据保护级别决定安全策略
TEE_Result secure_operation_with_time_check(void)
{
    uint32_t protection_level;
    TEE_Result res;

    res = TEE_GetPropertyAsU32(TEE_PROPSET_TEE_IMPLEMENTATION,
                               "gpd.tee.systemTime.protectionLevel",
                               &protection_level);
    if (res != TEE_SUCCESS)
        return res;

    if (protection_level >= 1000) {
        // 高安全级别：可以执行时间敏感的安全操作
        return perform_high_security_operation();
    } else {
        // 低安全级别：需要额外的验证措施
        return perform_operation_with_additional_checks();
    }
}
```

**2. 许可证验证**：
```c
// 许可证验证示例
TEE_Result validate_license_expiration(void)
{
    uint32_t sys_protection_level, ta_protection_level;
    TEE_Time current_time, license_expiry;
    TEE_Result res;

    // 查询保护级别
    res = TEE_GetPropertyAsU32(TEE_PROPSET_TEE_IMPLEMENTATION,
                               "gpd.tee.systemTime.protectionLevel",
                               &sys_protection_level);
    if (res != TEE_SUCCESS)
        return res;

    res = TEE_GetPropertyAsU32(TEE_PROPSET_TEE_IMPLEMENTATION,
                               "gpd.tee.TAPersistentTime.protectionLevel",
                               &ta_protection_level);
    if (res != TEE_SUCCESS)
        return res;

    // 根据保护级别选择时间源
    if (sys_protection_level >= 1000) {
        TEE_GetSystemTime(&current_time);
    } else if (ta_protection_level >= 1000) {
        res = TEE_GetTAPersistentTime(&current_time);
        if (res != TEE_SUCCESS)
            return res;
    } else {
        // 保护级别不足，拒绝验证
        return TEE_ERROR_SECURITY;
    }

    // 执行许可证过期检查
    if (TEE_TIME_LT(license_expiry, current_time)) {
        return TEE_ERROR_ACCESS_DENIED;
    }

    return TEE_SUCCESS;
}
```

**3. 审计日志记录**：
```c
// 审计日志示例
TEE_Result log_security_event(const char *event_description)
{
    uint32_t protection_level;
    TEE_Time timestamp;
    TEE_Result res;

    res = TEE_GetPropertyAsU32(TEE_PROPSET_TEE_IMPLEMENTATION,
                               "gpd.tee.systemTime.protectionLevel",
                               &protection_level);
    if (res != TEE_SUCCESS)
        return res;

    TEE_GetSystemTime(&timestamp);

    // 记录事件时包含时间保护级别信息
    DMSG("Security Event: %s, Time: %u.%03u, Protection Level: %u",
         event_description, timestamp.seconds, timestamp.millis, protection_level);

    return TEE_SUCCESS;
}
```

### 6.2 安全考量

**1. 保护级别降级攻击防护**：
```c
// 防护示例：检测保护级别是否被恶意降级
static uint32_t expected_protection_level = 1000;

TEE_Result check_protection_level_integrity(void)
{
    uint32_t current_level;
    TEE_Result res;

    res = TEE_GetPropertyAsU32(TEE_PROPSET_TEE_IMPLEMENTATION,
                               "gpd.tee.systemTime.protectionLevel",
                               &current_level);
    if (res != TEE_SUCCESS)
        return res;

    if (current_level < expected_protection_level) {
        EMSG("Protection level downgrade detected: expected %u, got %u",
             expected_protection_level, current_level);
        return TEE_ERROR_SECURITY;
    }

    return TEE_SUCCESS;
}
```

**2. 时间源一致性验证**：
```c
// 验证不同时间源的一致性
TEE_Result verify_time_source_consistency(void)
{
    TEE_Time sys_time, ree_time;
    uint32_t protection_level;
    TEE_Result res;

    res = TEE_GetPropertyAsU32(TEE_PROPSET_TEE_IMPLEMENTATION,
                               "gpd.tee.systemTime.protectionLevel",
                               &protection_level);
    if (res != TEE_SUCCESS)
        return res;

    TEE_GetSystemTime(&sys_time);
    TEE_GetREETime(&ree_time);

    // 计算时间差
    uint32_t diff_seconds = abs((int32_t)sys_time.seconds - (int32_t)ree_time.seconds);

    // 如果是高保护级别的系统时间，但与REE时间差异过大，可能存在问题
    if (protection_level >= 1000 && diff_seconds > 60) {
        EMSG("Large time discrepancy detected: sys=%u, ree=%u, diff=%u",
             sys_time.seconds, ree_time.seconds, diff_seconds);
        return TEE_ERROR_TIME_NOT_SET;
    }

    return TEE_SUCCESS;
}
```

## 7. 平台配置和时间源选择

### 7.1 编译时配置选项

**主要配置选项**：
```makefile
# 启用ARM通用计时器作为安全时间源
CFG_SECURE_TIME_SOURCE_CNTPCT=y

# 启用REE时间作为时间源（回退选项）
CFG_SECURE_TIME_SOURCE_REE=y

# 启用RTC驱动支持
CFG_DRIVERS_RTC=y
```

**平台特定配置示例**：
```makefile
# Sunxi平台 - 强制使用ARM通用计时器
$(call force,CFG_SECURE_TIME_SOURCE_CNTPCT,y)

# TI AM43xx平台 - 使用REE时间源
$(call force,CFG_SECURE_TIME_SOURCE_REE,y)

# TotalCompute平台 - 使用ARM通用计时器
$(call force,CFG_SECURE_TIME_SOURCE_CNTPCT,y)
```

### 7.2 运行时时间源选择

**时间源注册机制**：
```c
// core/drivers/atmel_tcb.c
static const struct time_source atmel_tcb_time_source = {
    .name = "atmel_tcb",
    .protection_level = 1000,
    .get_sys_time = atmel_tcb_get_sys_time,
};

REGISTER_TIME_SOURCE(atmel_tcb_time_source)
```

**选择优先级**：
1. 最高保护级别的可用时间源
2. 硬件计时器优先于软件时间源
3. 专用计时器优先于通用计时器

### 7.3 时间源切换和故障处理

**故障检测机制**：
```c
// 时间源健康检查示例
TEE_Result check_time_source_health(void)
{
    TEE_Time time1, time2;
    TEE_Result res;

    // 获取两次时间读取
    res = tee_time_get_sys_time(&time1);
    if (res != TEE_SUCCESS)
        return res;

    tee_time_busy_wait(100); // 等待100ms

    res = tee_time_get_sys_time(&time2);
    if (res != TEE_SUCCESS)
        return res;

    // 检查时间是否正常递增
    if (!TEE_TIME_LT(time1, time2)) {
        EMSG("Time source not advancing properly");
        return TEE_ERROR_BAD_STATE;
    }

    return TEE_SUCCESS;
}
```

## 8. 总结

### 8.1 保护级别机制总结

OP-TEE的时间保护级别机制提供了以下特性：

1. **明确的安全等级定义**：100级和1000级分别对应不同的安全保证
2. **灵活的硬件适配**：支持多种硬件计时器实现
3. **标准化的查询接口**：符合GP规范的属性查询机制
4. **编译时和运行时配置**：支持平台特定的时间源选择

### 8.2 实现优势

1. **硬件抽象良好**：统一的接口支持不同硬件平台
2. **安全性分级明确**：应用可以根据保护级别做出安全决策
3. **标准兼容性**：完全符合GP规范要求
4. **可扩展性强**：支持新的时间源注册和集成

### 8.3 使用建议

1. **安全应用**：优先使用保护级别1000的时间源
2. **兼容性考虑**：在不同平台上验证时间保护级别
3. **故障处理**：实现时间源健康检查和故障恢复机制
4. **安全策略**：根据保护级别调整安全策略的严格程度

OP-TEE的时间保护级别机制为安全应用提供了可靠的时间安全保障，通过合理使用这些机制，可以构建更加安全和可靠的TEE应用。
