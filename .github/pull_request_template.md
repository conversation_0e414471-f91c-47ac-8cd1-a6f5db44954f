<!--
    If you are new to submitting pull requests to OP-TEE, then please have a
    look at the list below and tick them off before submitting the pull request.

    1. Read our contribution guidelines:
         https://optee.readthedocs.io/en/latest/general/contribute.html

    2. Read the contribution section in Notice.md and pay extra attention to the
       "Developer Certificate of Origin" in the contribution guidelines.

    3. You should run checkpatch preferably before submitting the pull request.

    4. When everything has been reviewed, you will need to squash, rebase and
       add tags like `Reviewed-by`, `Acked-by`, `Tested-by` etc. More details
       about this can also be found on the link provided above.

    NOTE: This comment will not be shown in the pull request, so no harm keeping
    it, but feel free to remove it if you like.
-->
