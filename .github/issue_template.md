<!--
    General guidance when creating issues:

    1. Please try to remember to close the issue when you have
       got an answer to your question.

    2. It never hurts to state which commit or release tag you are using in case
       the question is about build issues.

    3. Try to use GitHub markdown formatting to make your issue more readable:
         https://help.github.com/en/github/writing-on-github/basic-writing-and-formatting-syntax#quoting-code

    4. Try to search for the issue before posting the question:
         -> Issues tab -> Filters

    5. Check the FAQ before posting a question:
         https://optee.readthedocs.io/en/latest/faq/faq.html

    NOTE: This comment will not be shown in the issue, so no harm keeping it,
    but feel free to remove it if you like.
-->
