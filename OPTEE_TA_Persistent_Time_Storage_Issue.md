# TA Persistent Time Storage Non-Compliance with GP Specification: Memory-Only Storage Causes Data Loss After Reboot

## Problem Description

The current OP-TEE implementation has a critical flaw in the TA Persistent Time storage mechanism: **time offset data is only stored in memory and is completely lost after system reboot**. This violates the fundamental definition of "persistent" and does not comply with the explicit requirements of the GlobalPlatform TEE Internal Core API v1.31 specification.

### Core Issues
- TA persistent time set via `TEE_SetTAPersistentTime()` is lost after system reboot
- After each reboot, all TA persistent times are reset to "not set" state
- This prevents security applications that depend on persistent time from functioning correctly

## Technical Details

### Current Implementation Analysis

**Problem Code Location**: `core/tee/tee_time_generic.c`

**Data Structure Definition**:
```c
struct tee_ta_time_offs {
    TEE_UUID uuid;    // TA unique identifier
    TEE_Time offs;    // Time offset
    bool positive;    // Offset direction
};

static struct tee_ta_time_offs *tee_time_offs;  // Global static array (memory storage)
static size_t tee_time_num_offs;                // Array size
```

**Problem Function**: `tee_time_ta_set_offs()`
```c
static TEE_Result tee_time_ta_set_offs(const TEE_UUID *uuid,
                                       const TEE_Time *offs, bool positive)
{
    // ... existing record lookup logic ...

    // ISSUE: Using realloc() to dynamically allocate storage space in memory
    n = tee_time_num_offs + 1;
    o = realloc(tee_time_offs, n * sizeof(struct tee_ta_time_offs));
    if (!o)
        return TEE_ERROR_OUT_OF_MEMORY;

    // ISSUE: Data is only stored in memory, lost after reboot
    tee_time_offs = o;
    tee_time_offs[tee_time_num_offs].uuid = *uuid;
    tee_time_offs[tee_time_num_offs].offs = *offs;
    tee_time_offs[tee_time_num_offs].positive = positive;
    tee_time_num_offs = n;
    return TEE_SUCCESS;
}
```

### Storage Lifecycle Issues

| Stage | Current Behavior | Expected Behavior |
|-------|------------------|-------------------|
| TA sets time | ✅ Store to memory array | ✅ Store to TEE Trusted Storage |
| During system runtime | ✅ Normal read and use | ✅ Normal read and use |
| After system reboot | ❌ Data completely lost | ✅ Restore data from secure storage |
| After power cycle | ❌ Data completely lost | ✅ Restore data from secure storage |

## GP Specification Compliance Analysis

### Violated GP Specification Requirements

**GlobalPlatform TEE Internal Core API v1.31 specification explicitly requires**:

The comments in `core/tee/tee_svc.c` already clearly state the GP specification requirements:
```c
/*
 * TA persistent time protection level
 * 100: Persistent time based on an REE-controlled real-time clock
 * and on the TEE Trusted Storage for the storage of origins (default).
 * 1000: Persistent time based on a TEE-controlled real-time clock
 * and the TEE Trusted Storage.
 * The real-time clock MUST be out of reach of software attacks
 * from the REE.
 */
```

**Key Requirements**:
1. **MUST use TEE Trusted Storage for storing time origins**
2. **Both protection levels 100 and 1000 require TEE Trusted Storage**
3. **Time data MUST persist across device reboots**

### Current Implementation Non-Compliance

| GP Specification Requirement | Current Implementation | Compliance Level |
|------------------------------|------------------------|------------------|
| Use TEE Trusted Storage | ❌ Memory-only storage | **Non-compliant** |
| Persist across reboots | ❌ Data lost after reboot | **Non-compliant** |
| Secure storage of time origins | ❌ Memory storage, insufficient security | **Non-compliant** |
| API functional correctness | ✅ Basic functionality works | Compliant |

## Impact Scope

### Affected API Functions
- `TEE_SetTAPersistentTime()` - Set time is lost after reboot
- `TEE_GetTAPersistentTime()` - Returns `TEE_ERROR_TIME_NOT_SET` after reboot

### Affected Application Scenarios
1. **Security Authentication Applications**: Rely on persistent time for timestamp verification
2. **Digital Certificate Applications**: Need persistent time for certificate validity checks
3. **Audit Logging Applications**: Require reliable time baseline for log recording
4. **Time-Sensitive Cryptographic Applications**: Depend on persistent time for key lifecycle management

## Reproduction Steps

1. Start OP-TEE system
2. Call `TEE_SetTAPersistentTime()` in a TA to set persistent time
3. Verify `TEE_GetTAPersistentTime()` correctly returns the set time
4. Reboot OP-TEE system
5. Call `TEE_GetTAPersistentTime()` again
6. **Observe**: Returns `TEE_ERROR_TIME_NOT_SET`, time data is lost

## Proposed Solution

### Solution Overview
Migrate TA time offset data from memory storage to TEE Trusted Storage to implement true persistent storage.

### Detailed Implementation Suggestions

1. **Define Persistent Storage Format**:
```c
#define TA_TIME_STORAGE_ID "optee.ta_persistent_time"

struct ta_time_persistent_data {
    uint32_t version;           // Data format version
    uint32_t count;             // Number of TA time records
    struct tee_ta_time_offs entries[];  // TA time offset array
};
```

2. **Implement Persistent Storage Functions**:
```c
TEE_Result tee_time_load_persistent_data(void);   // Load at system startup
TEE_Result tee_time_save_persistent_data(void);   // Save when setting
```

3. **Modify Existing Functions**:
   - Add persistent save logic to `tee_time_ta_set_offs()`
   - Call load function during system initialization to restore data

4. **Error Handling**:
   - Handle insufficient storage space scenarios
   - Provide recovery mechanism for data corruption
   - Backward compatibility handling

### Implementation Priority
- **High Priority**: Basic persistent storage functionality
- **Medium Priority**: Error recovery and data migration
- **Low Priority**: Performance optimization and advanced features

## Related Files

Main files that need modification:
- `core/tee/tee_time_generic.c` - Main implementation file
- `core/include/kernel/tee_time.h` - Header file declarations
- `core/tee/tee_svc.c` - System call layer (may need adjustments)

## Testing Recommendations

1. **Functional Testing**: Verify basic persistent storage functionality
2. **Reboot Testing**: Verify data recovery after system reboot
3. **Stress Testing**: Test storage performance with large amounts of TA time data
4. **Compatibility Testing**: Ensure existing TA applications are not affected
5. **Security Testing**: Verify security and integrity of stored data

## Priority Assessment

**Severity**: 🔴 **High** - Violates GP specification, affects core functionality
**Impact Scope**: 🔴 **Wide** - Affects all applications using TA persistent time
**Fix Complexity**: 🟡 **Medium** - Requires storage mechanism refactoring, but technical solution is clear
**Backward Compatibility**: 🟢 **Good** - Can maintain API interface unchanged

---

**Labels**: `bug`, `gp-compliance`, `persistent-storage`, `time-api`, `high-priority`
Is this implementation allowed by gp and can it pass gp authentication I hope you guys can answer my question.Thank you.